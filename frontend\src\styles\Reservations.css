/* Reservations Page Styles */
.reservations-page {
  padding: 40px 0;
  min-height: calc(100vh - 160px);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 50px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.reservations-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Reservation Form */
.reservation-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.reservation-form h2 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.8rem;
  border-bottom: 3px solid #e74c3c;
  padding-bottom: 10px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #e74c3c;
  background: white;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  border-color: #bdc3c7;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.6;
}

/* Time Slots */
.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.time-slot {
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.time-slot:hover {
  border-color: #e74c3c;
  background: #fef5f5;
}

.time-slot.selected {
  background: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

.time-slot.unavailable {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Party Size Selector */
.party-size-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.party-size-btn {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1rem;
}

.party-size-btn:hover {
  border-color: #e74c3c;
  background: #fef5f5;
}

.party-size-btn.selected {
  background: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

/* Submit Button */
.submit-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 20px;
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
}

.submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Availability Info */
.availability-info {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  color: #155724;
}

.availability-info.warning {
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.availability-info.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

/* My Reservations */
.my-reservations {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.my-reservations h2 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.8rem;
  border-bottom: 3px solid #e74c3c;
  padding-bottom: 10px;
}

.reservations-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reservation-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.reservation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.reservation-date {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.reservation-status {
  padding: 6px 12px;
  border-radius: 15px;
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reservation-status.confirmed {
  background: #d4edda;
  color: #155724;
}

.reservation-status.pending {
  background: #fff3cd;
  color: #856404;
}

.reservation-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.reservation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.detail-value {
  color: #555;
  font-size: 1rem;
}

.reservation-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.action-btn.primary {
  background: #e74c3c;
  color: white;
}

.action-btn.primary:hover {
  background: #c0392b;
}

.action-btn.danger {
  background: #dc3545;
  color: white;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Empty State */
.empty-reservations {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-reservations h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.empty-reservations p {
  margin-bottom: 20px;
  font-size: 1rem;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reservations-page {
    padding: 20px 0;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .reservations-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .reservation-form,
  .my-reservations {
    padding: 25px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .time-slots {
    grid-template-columns: repeat(3, 1fr);
  }

  .party-size-selector {
    grid-template-columns: repeat(4, 1fr);
  }

  .reservation-details {
    grid-template-columns: 1fr;
  }

  .reservation-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.8rem;
  }

  .reservation-form,
  .my-reservations {
    padding: 20px;
  }

  .time-slots {
    grid-template-columns: repeat(2, 1fr);
  }

  .party-size-selector {
    grid-template-columns: repeat(3, 1fr);
  }

  .reservation-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
