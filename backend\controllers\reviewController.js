const Review = require('../models/Review');
const Order = require('../models/Order');
const Dish = require('../models/Dish');
const { validationResult } = require('express-validator');

// @desc    Create a review
// @route   POST /api/reviews
// @access  Private
const createReview = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { dishId, orderId, rating, title, comment } = req.body;

    // Check if order exists and belongs to user
    const order = await Order.findOne({ 
      _id: orderId, 
      customer: req.user._id,
      status: 'delivered'
    }).populate('items.dish');

    if (!order) {
      return res.status(404).json({ 
        message: 'Order not found or not delivered yet' 
      });
    }

    // Check if dish was in the order
    const dishInOrder = order.items.find(item => 
      item.dish._id.toString() === dishId
    );

    if (!dishInOrder) {
      return res.status(400).json({ 
        message: 'You can only review dishes you have ordered' 
      });
    }

    // Check if review already exists
    const existingReview = await Review.findOne({
      customer: req.user._id,
      dish: dishId,
      order: orderId
    });

    if (existingReview) {
      return res.status(400).json({ 
        message: 'You have already reviewed this dish for this order' 
      });
    }

    // Create review
    const reviewData = {
      customer: req.user._id,
      dish: dishId,
      order: orderId,
      rating,
      title,
      comment
    };

    // Handle image uploads if any
    if (req.files && req.files.length > 0) {
      reviewData.images = req.files.map(file => file.filename);
    }

    const review = await Review.create(reviewData);

    // Populate the review
    const populatedReview = await Review.findById(review._id)
      .populate('customer', 'name')
      .populate('dish', 'name image');

    res.status(201).json(populatedReview);
  } catch (error) {
    console.error('Create review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get reviews for a dish
// @route   GET /api/reviews/dish/:dishId
// @access  Public
const getDishReviews = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      sortBy = 'createdAt',
      sortOrder = 'desc',
      rating 
    } = req.query;

    // Build query
    let query = { 
      dish: req.params.dishId, 
      isApproved: true 
    };

    if (rating) {
      query.rating = parseInt(rating);
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const reviews = await Review.find(query)
      .populate('customer', 'name')
      .populate('adminResponse.respondedBy', 'name')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum);

    const total = await Review.countDocuments(query);

    // Get rating distribution
    const ratingStats = await Review.aggregate([
      { $match: { dish: req.params.dishId, isApproved: true } },
      { $group: { _id: '$rating', count: { $sum: 1 } } },
      { $sort: { _id: -1 } }
    ]);

    res.json({
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      },
      ratingStats
    });
  } catch (error) {
    console.error('Get dish reviews error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get user's reviews
// @route   GET /api/reviews/my-reviews
// @access  Private
const getUserReviews = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const reviews = await Review.find({ customer: req.user._id })
      .populate('dish', 'name image price')
      .populate('order', 'orderNumber createdAt')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await Review.countDocuments({ customer: req.user._id });

    res.json({
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get user reviews error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update a review
// @route   PUT /api/reviews/:id
// @access  Private
const updateReview = async (req, res) => {
  try {
    const { rating, title, comment } = req.body;

    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user owns the review
    if (review.customer.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Update review
    review.rating = rating || review.rating;
    review.title = title || review.title;
    review.comment = comment || review.comment;

    // Handle new image uploads
    if (req.files && req.files.length > 0) {
      review.images = [...review.images, ...req.files.map(file => file.filename)];
    }

    await review.save();

    const updatedReview = await Review.findById(review._id)
      .populate('customer', 'name')
      .populate('dish', 'name image');

    res.json(updatedReview);
  } catch (error) {
    console.error('Update review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete a review
// @route   DELETE /api/reviews/:id
// @access  Private
const deleteReview = async (req, res) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user owns the review or is admin
    if (review.customer.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    await Review.findByIdAndDelete(req.params.id);

    res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Delete review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Vote on review helpfulness
// @route   POST /api/reviews/:id/vote
// @access  Private
const voteOnReview = async (req, res) => {
  try {
    const { voteType } = req.body; // 'helpful' or 'not-helpful'
    
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user already voted
    const existingVote = review.votedBy.find(
      vote => vote.user.toString() === req.user._id.toString()
    );

    if (existingVote) {
      // Update existing vote
      existingVote.voteType = voteType;
    } else {
      // Add new vote
      review.votedBy.push({
        user: req.user._id,
        voteType
      });
    }

    // Recalculate helpful votes
    review.helpfulVotes = review.votedBy.filter(
      vote => vote.voteType === 'helpful'
    ).length;

    await review.save();

    res.json({ message: 'Vote recorded successfully', helpfulVotes: review.helpfulVotes });
  } catch (error) {
    console.error('Vote on review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get all reviews (Admin)
// @route   GET /api/reviews/admin/all
// @access  Private/Admin
const getAllReviews = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20,
      isApproved,
      rating,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    let query = {};
    if (isApproved !== undefined) {
      query.isApproved = isApproved === 'true';
    }
    if (rating) {
      query.rating = parseInt(rating);
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const reviews = await Review.find(query)
      .populate('customer', 'name email')
      .populate('dish', 'name image')
      .populate('order', 'orderNumber')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum);

    const total = await Review.countDocuments(query);

    res.json({
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get all reviews error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Approve/reject review (Admin)
// @route   PUT /api/reviews/:id/approve
// @access  Private/Admin
const approveReview = async (req, res) => {
  try {
    const { isApproved } = req.body;
    
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    review.isApproved = isApproved;
    await review.save();

    res.json({ message: `Review ${isApproved ? 'approved' : 'rejected'} successfully` });
  } catch (error) {
    console.error('Approve review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Add admin response to review
// @route   POST /api/reviews/:id/respond
// @access  Private/Admin
const respondToReview = async (req, res) => {
  try {
    const { message } = req.body;
    
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    review.adminResponse = {
      message,
      respondedBy: req.user._id,
      respondedAt: new Date()
    };

    await review.save();

    const updatedReview = await Review.findById(review._id)
      .populate('customer', 'name')
      .populate('dish', 'name image')
      .populate('adminResponse.respondedBy', 'name');

    res.json(updatedReview);
  } catch (error) {
    console.error('Respond to review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createReview,
  getDishReviews,
  getUserReviews,
  updateReview,
  deleteReview,
  voteOnReview,
  getAllReviews,
  approveReview,
  respondToReview
};
