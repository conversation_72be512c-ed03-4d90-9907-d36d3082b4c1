import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import Cart from './Cart';
import '../styles/Navbar.css';

const Navbar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const { totalItems } = useCart();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <>
      <nav className="navbar">
        <div className="nav-container">
          <Link to="/" className="nav-logo" onClick={closeMenu}>
            🍽️ Delicious Restaurant
          </Link>
          
          <div className={`nav-menu ${isMenuOpen ? 'active' : ''}`}>
            <Link to="/" className="nav-link" onClick={closeMenu}>
              Home
            </Link>
            <Link to="/menu" className="nav-link" onClick={closeMenu}>
              Menu
            </Link>
            <Link to="/reservations" className="nav-link" onClick={closeMenu}>
              Reservations
            </Link>
            <Link to="/contact" className="nav-link" onClick={closeMenu}>
              Contact
            </Link>
            
            {isAuthenticated ? (
              <>
                <Link to="/orders" className="nav-link" onClick={closeMenu}>
                  My Orders
                </Link>
                <Link to="/loyalty" className="nav-link" onClick={closeMenu}>
                  Rewards
                </Link>
                <Link to="/profile" className="nav-link" onClick={closeMenu}>
                  Profile
                </Link>
                {user?.role === 'admin' && (
                  <Link to="/admin/dashboard" className="nav-link admin-link" onClick={closeMenu}>
                    Admin
                  </Link>
                )}
                <button className="nav-link logout-btn" onClick={handleLogout}>
                  Logout
                </button>
              </>
            ) : (
              <>
                <Link to="/login" className="nav-link" onClick={closeMenu}>
                  Login
                </Link>
                <Link to="/register" className="nav-link" onClick={closeMenu}>
                  Register
                </Link>
              </>
            )}
            
            <button className="cart-btn" onClick={toggleCart}>
              🛒 Cart {totalItems > 0 && <span className="cart-count">{totalItems}</span>}
            </button>
          </div>
          
          <div className="nav-toggle" onClick={toggleMenu}>
            <span className="bar"></span>
            <span className="bar"></span>
            <span className="bar"></span>
          </div>
        </div>
      </nav>
      
      {/* Cart Sidebar */}
      <Cart isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
    </>
  );
};

export default Navbar;
