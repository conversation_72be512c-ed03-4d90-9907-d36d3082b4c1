const express = require('express');
const { body } = require('express-validator');
const {
  getLoyaltyProfile,
  getLoyaltyTransactions,
  redeemPoints,
  applyReferralCode,
  getLeaderboard,
  getLoyaltyStats
} = require('../controllers/loyaltyController');
const { protect, admin } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const redeemValidation = [
  body('points')
    .isInt({ min: 100 })
    .withMessage('Minimum redemption is 100 points'),
  body('rewardType')
    .isIn(['discount', 'free_item'])
    .withMessage('Invalid reward type')
];

const referralValidation = [
  body('referralCode')
    .isLength({ min: 6, max: 10 })
    .withMessage('Invalid referral code format')
];

// Public routes
router.get('/leaderboard', getLeaderboard);

// Protected routes
router.get('/profile', protect, getLoyaltyProfile);
router.get('/transactions', protect, getLoyaltyTransactions);
router.post('/redeem', protect, redeemValidation, redeemPoints);
router.post('/referral', protect, referralValidation, applyReferralCode);

// Admin routes
router.get('/admin/stats', protect, admin, getLoyaltyStats);

module.exports = router;
