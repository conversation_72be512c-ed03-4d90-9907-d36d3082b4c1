import React, { useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import '../styles/ReviewForm.css';

const ReviewForm = ({ dishId, orderId, dishName, onReviewSubmitted, onClose }) => {
  const [formData, setFormData] = useState({
    rating: 5,
    title: '',
    comment: ''
  });
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 5) {
      toast.error('You can upload maximum 5 images');
      return;
    }
    setImages(files);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = new FormData();
      submitData.append('dishId', dishId);
      submitData.append('orderId', orderId);
      submitData.append('rating', formData.rating);
      submitData.append('title', formData.title);
      submitData.append('comment', formData.comment);

      // Add images
      images.forEach(image => {
        submitData.append('images', image);
      });

      await axios.post('/api/reviews', submitData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      toast.success('Review submitted successfully!');
      onReviewSubmitted();
      onClose();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to submit review');
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating, interactive = false) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`star ${index < rating ? 'filled' : ''} ${interactive ? 'interactive' : ''}`}
        onClick={interactive ? () => setFormData({ ...formData, rating: index + 1 }) : undefined}
      >
        ⭐
      </span>
    ));
  };

  return (
    <div className="review-form-modal">
      <div className="review-form-content">
        <div className="review-form-header">
          <h2>Write a Review for {dishName}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="review-form">
          <div className="form-group">
            <label>Rating *</label>
            <div className="rating-input">
              {renderStars(formData.rating, true)}
              <span className="rating-text">
                {formData.rating} out of 5 stars
              </span>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="title">Review Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Summarize your experience..."
              required
              maxLength="100"
            />
            <small>{formData.title.length}/100 characters</small>
          </div>

          <div className="form-group">
            <label htmlFor="comment">Your Review *</label>
            <textarea
              id="comment"
              name="comment"
              value={formData.comment}
              onChange={handleChange}
              placeholder="Tell others about your experience with this dish..."
              rows="5"
              required
              maxLength="500"
            />
            <small>{formData.comment.length}/500 characters</small>
          </div>

          <div className="form-group">
            <label htmlFor="images">Add Photos (Optional)</label>
            <input
              type="file"
              id="images"
              accept="image/*"
              multiple
              onChange={handleImageChange}
            />
            <small>You can upload up to 5 images (JPG, PNG, GIF)</small>
            {images.length > 0 && (
              <div className="image-preview">
                {images.map((image, index) => (
                  <div key={index} className="preview-item">
                    <img 
                      src={URL.createObjectURL(image)} 
                      alt={`Preview ${index + 1}`}
                      className="preview-image"
                    />
                    <span className="image-name">{image.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="form-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Submitting...' : 'Submit Review'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReviewForm;
