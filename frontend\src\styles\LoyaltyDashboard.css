.loyalty-dashboard {
  padding: 40px 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.loyalty-header {
  text-align: center;
  margin-bottom: 40px;
}

.loyalty-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.loyalty-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Loyalty Overview */
.loyalty-overview {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tier-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
}

.tier-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.tier-icon {
  font-size: 3rem;
}

.tier-details h2 {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
}

.tier-details p {
  margin: 0;
  color: #7f8c8d;
}

.points-summary {
  display: flex;
  gap: 30px;
}

.points-item {
  text-align: center;
}

.points-value {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #e74c3c;
}

.points-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* Tier Progress */
.tier-progress {
  margin-bottom: 30px;
}

.tier-progress h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #ecf0f1;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #f39c12);
  transition: width 0.3s ease;
}

.tier-progress p {
  color: #7f8c8d;
  margin: 0;
}

/* Tier Benefits */
.tier-benefits h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: 600;
}

.benefit-icon {
  font-size: 1.2rem;
}

/* Alerts */
.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 600;
}

.alert-warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Tabs */
.loyalty-tabs {
  display: flex;
  background: white;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab {
  flex: 1;
  padding: 15px 20px;
  background: white;
  border: none;
  cursor: pointer;
  font-weight: 600;
  color: #7f8c8d;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.tab.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
  background: #f8f9fa;
}

/* Tab Content */
.tab-content {
  background: white;
  border-radius: 0 0 12px 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Achievements */
.achievements-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.achievement-card {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.achievement-card::before {
  content: '🏆';
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 3rem;
  opacity: 0.3;
}

.achievement-card h4 {
  margin: 0 0 10px 0;
  font-size: 1.2rem;
}

.achievement-card p {
  margin: 0 0 15px 0;
  opacity: 0.9;
}

.achievement-points {
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: bold;
  display: inline-block;
  margin-bottom: 10px;
}

.achievement-date {
  font-size: 12px;
  opacity: 0.8;
}

/* Redeem Section */
.redeem-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.redeem-calculator {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
}

.redeem-calculator label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #2c3e50;
}

.redeem-calculator input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 16px;
}

.redeem-calculator p {
  font-size: 1.2rem;
  font-weight: bold;
  color: #27ae60;
  margin-bottom: 20px;
}

.redeem-info h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.redeem-info ul {
  list-style: none;
  padding: 0;
}

.redeem-info li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  color: #7f8c8d;
}

.redeem-info li:last-child {
  border-bottom: none;
}

/* Referral Section */
.referral-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.my-referral,
.apply-referral {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
}

.referral-code-display {
  display: flex;
  gap: 10px;
  margin: 15px 0;
}

.referral-code {
  flex: 1;
  padding: 15px;
  background: white;
  border: 2px solid #e74c3c;
  border-radius: 8px;
  font-family: monospace;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  color: #e74c3c;
}

.copy-btn {
  padding: 15px 20px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
}

.copy-btn:hover {
  background: #c0392b;
}

.referral-stats h4 {
  color: #2c3e50;
  margin: 20px 0 15px 0;
}

.referral-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.referral-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: white;
  border-radius: 6px;
  font-size: 14px;
}

.referral-input {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.referral-input input {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

/* History */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ddd;
}

.transaction-item.earned {
  border-left-color: #27ae60;
}

.transaction-item.redeemed {
  border-left-color: #e74c3c;
}

.transaction-item.bonus {
  border-left-color: #f39c12;
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.transaction-description {
  font-weight: 600;
  color: #2c3e50;
}

.transaction-date {
  font-size: 14px;
  color: #7f8c8d;
}

.transaction-points {
  font-weight: bold;
  font-size: 1.1rem;
}

.transaction-points.earned {
  color: #27ae60;
}

.transaction-points.redeemed {
  color: #e74c3c;
}

.transaction-points.bonus {
  color: #f39c12;
}

/* Leaderboard */
.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.leaderboard-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.rank {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e74c3c;
  min-width: 40px;
}

.member-name {
  flex: 1;
  font-weight: 600;
  color: #2c3e50;
}

.member-tier {
  font-size: 1.5rem;
}

.member-points {
  font-weight: bold;
  color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .loyalty-dashboard {
    padding: 20px 0;
  }

  .tier-card {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .points-summary {
    justify-content: center;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .loyalty-tabs {
    flex-direction: column;
  }

  .redeem-section,
  .referral-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .referral-code-display,
  .referral-input {
    flex-direction: column;
  }

  .leaderboard-item {
    flex-wrap: wrap;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .loyalty-header h1 {
    font-size: 2rem;
  }

  .tier-card,
  .tab-content {
    padding: 20px 15px;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .transaction-item,
  .leaderboard-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
