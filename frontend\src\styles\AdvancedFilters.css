/* Advanced Filters Styles */
.advanced-filters {
  position: relative;
  margin-bottom: 20px;
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 1rem;
  font-weight: 500;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-toggle:hover {
  border-color: #e74c3c;
  background: #fef5f5;
}

.filter-icon {
  font-size: 1.2rem;
}

.filter-count {
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

/* Filters Panel */
.filters-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  z-index: 100;
  margin-top: 8px;
  max-height: 600px;
  overflow-y: auto;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.filters-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.filters-actions {
  display: flex;
  gap: 10px;
}

.clear-filters {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: background 0.2s ease;
}

.clear-filters:hover {
  background: rgba(231, 76, 60, 0.1);
}

.close-filters {
  background: none;
  border: none;
  color: #666;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-filters:hover {
  background: #f8d7da;
  color: #721c24;
}

/* Filters Content */
.filters-content {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
  margin-bottom: 5px;
}

/* Price Range */
.price-range {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.range-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #e74c3c;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #e74c3c;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.price-display,
.time-display {
  text-align: center;
  font-weight: 600;
  color: #e74c3c;
  font-size: 0.9rem;
}

/* Checkbox Grid */
.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: background 0.2s ease;
  font-size: 0.9rem;
}

.checkbox-item:hover {
  background: #f8f9fa;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #e74c3c;
}

/* Select */
.filter-select {
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 0.95rem;
  background: white;
  color: #2c3e50;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #e74c3c;
}

/* Rating Filter */
.rating-filter {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rating-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.rating-btn {
  padding: 6px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rating-btn:hover {
  border-color: #e74c3c;
  background: #fef5f5;
}

.rating-btn.active {
  background: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

/* Toggle Filters */
.toggle-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background 0.2s ease;
  font-size: 0.9rem;
}

.toggle-item:hover {
  background: #f8f9fa;
}

.toggle-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #e74c3c;
}

/* Sort Options */
.sort-options {
  display: flex;
  gap: 8px;
  align-items: center;
}

.sort-options .filter-select {
  flex: 1;
}

.sort-order-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-order-btn:hover {
  background: #c0392b;
}

/* Filters Footer */
.filters-footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.apply-filters {
  width: 100%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-filters:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

/* Scrollbar */
.filters-panel::-webkit-scrollbar {
  width: 8px;
}

.filters-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.filters-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.filters-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters-content {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 16px;
  }

  .filters-header {
    padding: 16px;
  }

  .filters-footer {
    padding: 16px;
  }

  .checkbox-grid {
    grid-template-columns: 1fr;
  }

  .rating-buttons {
    justify-content: center;
  }

  .filters-panel {
    max-height: 500px;
  }
}

@media (max-width: 480px) {
  .filter-toggle {
    padding: 10px 14px;
    font-size: 0.95rem;
  }

  .filters-header h3 {
    font-size: 1.1rem;
  }

  .checkbox-item,
  .toggle-item {
    font-size: 0.85rem;
  }

  .rating-btn {
    font-size: 0.8rem;
    padding: 5px 10px;
  }

  .sort-options {
    flex-direction: column;
    gap: 10px;
  }

  .sort-order-btn {
    width: 100%;
    height: 36px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .filters-panel {
    border-color: #000;
  }

  .filter-toggle {
    border-color: #000;
  }

  .checkbox-item:hover,
  .toggle-item:hover {
    background: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .filters-panel {
    animation: none;
  }

  .filter-toggle,
  .rating-btn,
  .apply-filters {
    transition: none;
  }

  .apply-filters:hover {
    transform: none;
  }
}
