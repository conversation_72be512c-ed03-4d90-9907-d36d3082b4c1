import React, { useState, useEffect, createContext, useContext } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { QuickRating } from './Rating';
import { toast } from 'react-toastify';
import '../styles/Wishlist.css';

const WishlistContext = createContext();

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};

export const WishlistProvider = ({ children }) => {
  const [wishlist, setWishlist] = useState([]);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, user } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      fetchWishlist();
    } else {
      // Load from localStorage for non-authenticated users
      const savedWishlist = localStorage.getItem('wishlist');
      if (savedWishlist) {
        setWishlist(JSON.parse(savedWishlist));
      }
    }
  }, [isAuthenticated]);

  const fetchWishlist = async () => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    try {
      const response = await axios.get('/api/wishlist', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      setWishlist(response.data.items || []);
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      toast.error('Failed to load wishlist');
    } finally {
      setLoading(false);
    }
  };

  const addToWishlist = async (dish) => {
    if (isInWishlist(dish._id)) {
      toast.info('Item already in wishlist');
      return;
    }

    const newItem = {
      dishId: dish._id,
      dish: dish,
      addedAt: new Date()
    };

    if (isAuthenticated) {
      try {
        await axios.post('/api/wishlist', { dishId: dish._id }, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });
        setWishlist(prev => [newItem, ...prev]);
        toast.success('Added to wishlist!');
      } catch (error) {
        console.error('Error adding to wishlist:', error);
        toast.error('Failed to add to wishlist');
      }
    } else {
      // Save to localStorage for non-authenticated users
      const updated = [newItem, ...wishlist];
      setWishlist(updated);
      localStorage.setItem('wishlist', JSON.stringify(updated));
      toast.success('Added to wishlist!');
    }
  };

  const removeFromWishlist = async (dishId) => {
    if (isAuthenticated) {
      try {
        await axios.delete(`/api/wishlist/${dishId}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });
        setWishlist(prev => prev.filter(item => item.dishId !== dishId));
        toast.success('Removed from wishlist');
      } catch (error) {
        console.error('Error removing from wishlist:', error);
        toast.error('Failed to remove from wishlist');
      }
    } else {
      // Remove from localStorage for non-authenticated users
      const updated = wishlist.filter(item => item.dishId !== dishId);
      setWishlist(updated);
      localStorage.setItem('wishlist', JSON.stringify(updated));
      toast.success('Removed from wishlist');
    }
  };

  const isInWishlist = (dishId) => {
    return wishlist.some(item => item.dishId === dishId);
  };

  const clearWishlist = async () => {
    if (isAuthenticated) {
      try {
        await axios.delete('/api/wishlist', {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        });
        setWishlist([]);
        toast.success('Wishlist cleared');
      } catch (error) {
        console.error('Error clearing wishlist:', error);
        toast.error('Failed to clear wishlist');
      }
    } else {
      setWishlist([]);
      localStorage.removeItem('wishlist');
      toast.success('Wishlist cleared');
    }
  };

  const value = {
    wishlist,
    loading,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist,
    wishlistCount: wishlist.length
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
};

// Heart button component for adding/removing from wishlist
export const WishlistButton = ({ dish, size = 'medium', showText = false }) => {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const [isAnimating, setIsAnimating] = useState(false);
  
  const inWishlist = isInWishlist(dish._id);

  const handleClick = (e) => {
    e.stopPropagation();
    setIsAnimating(true);
    
    if (inWishlist) {
      removeFromWishlist(dish._id);
    } else {
      addToWishlist(dish);
    }

    setTimeout(() => setIsAnimating(false), 300);
  };

  return (
    <button 
      className={`wishlist-btn ${size} ${inWishlist ? 'active' : ''} ${isAnimating ? 'animating' : ''}`}
      onClick={handleClick}
      title={inWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      <span className="heart-icon">
        {inWishlist ? '❤️' : '🤍'}
      </span>
      {showText && (
        <span className="wishlist-text">
          {inWishlist ? 'Remove' : 'Add to Wishlist'}
        </span>
      )}
    </button>
  );
};

// Main wishlist page component
const WishlistPage = () => {
  const { wishlist, loading, clearWishlist } = useWishlist();
  const { addToCart } = useCart();
  const [sortBy, setSortBy] = useState('recent');

  const sortedWishlist = [...wishlist].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.dish.name.localeCompare(b.dish.name);
      case 'price-low':
        return a.dish.price - b.dish.price;
      case 'price-high':
        return b.dish.price - a.dish.price;
      case 'rating':
        return (b.dish.rating || 0) - (a.dish.rating || 0);
      case 'recent':
      default:
        return new Date(b.addedAt) - new Date(a.addedAt);
    }
  });

  const handleAddToCart = (dish) => {
    addToCart(dish, 1);
    toast.success(`${dish.name} added to cart!`);
  };

  if (loading) {
    return (
      <div className="wishlist-page">
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading your wishlist...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="wishlist-page">
      <div className="page-header">
        <h1>My Wishlist</h1>
        <p>Your favorite dishes saved for later</p>
      </div>

      {wishlist.length === 0 ? (
        <div className="empty-wishlist">
          <div className="empty-icon">💝</div>
          <h3>Your wishlist is empty</h3>
          <p>Start adding your favorite dishes to keep track of what you love!</p>
          <a href="/menu" className="browse-menu-btn">
            Browse Menu
          </a>
        </div>
      ) : (
        <>
          <div className="wishlist-controls">
            <div className="wishlist-stats">
              <span>{wishlist.length} item{wishlist.length !== 1 ? 's' : ''} saved</span>
            </div>
            
            <div className="wishlist-actions">
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="recent">Recently Added</option>
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
              </select>
              
              <button onClick={clearWishlist} className="clear-wishlist-btn">
                Clear All
              </button>
            </div>
          </div>

          <div className="wishlist-grid">
            {sortedWishlist.map((item) => (
              <div key={item.dishId} className="wishlist-item">
                <div className="item-image">
                  <img 
                    src={`http://localhost:5000/uploads/${item.dish.image}`}
                    alt={item.dish.name}
                    onError={(e) => {
                      e.target.src = '/api/placeholder/300/200';
                    }}
                  />
                  <WishlistButton dish={item.dish} size="small" />
                </div>
                
                <div className="item-content">
                  <h3>{item.dish.name}</h3>
                  <p className="item-description">{item.dish.description}</p>
                  
                  <div className="item-meta">
                    <span className="item-price">${item.dish.price}</span>
                    {item.dish.rating > 0 && (
                      <QuickRating value={item.dish.rating} size="small" />
                    )}
                  </div>
                  
                  <div className="item-actions">
                    <button 
                      onClick={() => handleAddToCart(item.dish)}
                      className="add-to-cart-btn"
                      disabled={!item.dish.isAvailable}
                    >
                      {item.dish.isAvailable ? 'Add to Cart' : 'Unavailable'}
                    </button>
                  </div>
                  
                  <div className="added-date">
                    Added {new Date(item.addedAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default WishlistPage;
