/* Profile Page Styles */
.profile-page {
  padding: 40px 0;
  min-height: calc(100vh - 160px);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Profile Info Card */
.profile-info {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.profile-info h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.4rem;
  border-bottom: 2px solid #e74c3c;
  padding-bottom: 10px;
}

.profile-info p {
  margin-bottom: 15px;
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
}

.profile-info strong {
  color: #2c3e50;
  font-weight: 600;
}

/* Profile Form */
.profile-form {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-form h3 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.4rem;
  border-bottom: 2px solid #e74c3c;
  padding-bottom: 10px;
}

.profile-form .form-group {
  margin-bottom: 25px;
}

.profile-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.profile-form input,
.profile-form select,
.profile-form textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
}

.profile-form input:focus,
.profile-form select:focus,
.profile-form textarea:focus {
  outline: none;
  border-color: #e74c3c;
  background: white;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.profile-form input:hover,
.profile-form select:hover,
.profile-form textarea:hover {
  border-color: #bdc3c7;
}

/* Address Section */
.address-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.address-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.address-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.address-grid .form-group {
  margin-bottom: 15px;
}

/* Submit Button */
.profile-form .submit-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 14px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 20px;
}

.profile-form .submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
}

.profile-form .submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading State */
.profile-form .submit-btn:disabled::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-left: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

/* Error Message */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

/* Profile Stats */
.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .profile-info,
  .profile-form {
    padding: 25px;
  }

  .address-grid {
    grid-template-columns: 1fr;
  }

  .profile-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .profile-page {
    padding: 20px 0;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .profile-info,
  .profile-form {
    padding: 20px;
  }

  .profile-form .submit-btn {
    padding: 12px 24px;
  }

  .profile-stats {
    grid-template-columns: 1fr;
  }
}

/* Animation for form elements */
.profile-form .form-group {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.profile-info:hover,
.profile-form:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* Focus indicators for accessibility */
.profile-form input:focus,
.profile-form select:focus,
.profile-form textarea:focus {
  outline: 2px solid #e74c3c;
  outline-offset: 2px;
}
