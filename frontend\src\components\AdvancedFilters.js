import React, { useState, useEffect } from 'react';
import { QuickRating } from './Rating';
import '../styles/AdvancedFilters.css';

const AdvancedFilters = ({ onFiltersChange, initialFilters = {} }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: [0, 100],
    categories: [],
    dietaryRestrictions: [],
    spiceLevel: '',
    rating: 0,
    preparationTime: 60,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isPopular: false,
    isAvailable: true,
    sortBy: 'name',
    sortOrder: 'asc',
    ...initialFilters
  });

  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  const categories = [
    'Appetizers', 'Main Course', 'Desserts', 'Beverages', 
    'Salads', 'Soups', 'Pasta', 'Pizza', 'Seafood', 'Meat'
  ];

  const dietaryOptions = [
    'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 
    'Nut-Free', 'Low-Carb', 'Keto', 'Halal', 'Kosher'
  ];

  const spiceLevels = [
    { value: '', label: 'Any' },
    { value: 'mild', label: 'Mild' },
    { value: 'medium', label: 'Medium' },
    { value: 'hot', label: 'Hot' },
    { value: 'extra-hot', label: 'Extra Hot' }
  ];

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'price', label: 'Price' },
    { value: 'rating', label: 'Rating' },
    { value: 'preparationTime', label: 'Prep Time' },
    { value: 'popularity', label: 'Popularity' }
  ];

  useEffect(() => {
    // Count active filters
    let count = 0;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 100) count++;
    if (filters.categories.length > 0) count++;
    if (filters.dietaryRestrictions.length > 0) count++;
    if (filters.spiceLevel) count++;
    if (filters.rating > 0) count++;
    if (filters.preparationTime < 60) count++;
    if (filters.isVegetarian || filters.isVegan || filters.isGlutenFree || filters.isPopular) count++;
    if (filters.sortBy !== 'name' || filters.sortOrder !== 'asc') count++;

    setActiveFiltersCount(count);
  }, [filters]);

  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleArrayFilterChange = (key, value, checked) => {
    setFilters(prev => ({
      ...prev,
      [key]: checked 
        ? [...prev[key], value]
        : prev[key].filter(item => item !== value)
    }));
  };

  const handlePriceRangeChange = (index, value) => {
    const newRange = [...filters.priceRange];
    newRange[index] = parseInt(value);
    setFilters(prev => ({
      ...prev,
      priceRange: newRange
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      priceRange: [0, 100],
      categories: [],
      dietaryRestrictions: [],
      spiceLevel: '',
      rating: 0,
      preparationTime: 60,
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isPopular: false,
      isAvailable: true,
      sortBy: 'name',
      sortOrder: 'asc'
    });
  };

  const toggleFilter = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="advanced-filters">
      <button 
        className="filter-toggle"
        onClick={toggleFilter}
      >
        <span className="filter-icon">🔍</span>
        Filters
        {activeFiltersCount > 0 && (
          <span className="filter-count">{activeFiltersCount}</span>
        )}
      </button>

      {isOpen && (
        <div className="filters-panel">
          <div className="filters-header">
            <h3>Filter Menu Items</h3>
            <div className="filters-actions">
              <button onClick={clearAllFilters} className="clear-filters">
                Clear All
              </button>
              <button onClick={toggleFilter} className="close-filters">
                ×
              </button>
            </div>
          </div>

          <div className="filters-content">
            {/* Price Range */}
            <div className="filter-group">
              <label className="filter-label">Price Range</label>
              <div className="price-range">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filters.priceRange[0]}
                  onChange={(e) => handlePriceRangeChange(0, e.target.value)}
                  className="range-slider"
                />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filters.priceRange[1]}
                  onChange={(e) => handlePriceRangeChange(1, e.target.value)}
                  className="range-slider"
                />
                <div className="price-display">
                  ${filters.priceRange[0]} - ${filters.priceRange[1]}
                </div>
              </div>
            </div>

            {/* Categories */}
            <div className="filter-group">
              <label className="filter-label">Categories</label>
              <div className="checkbox-grid">
                {categories.map(category => (
                  <label key={category} className="checkbox-item">
                    <input
                      type="checkbox"
                      checked={filters.categories.includes(category)}
                      onChange={(e) => handleArrayFilterChange('categories', category, e.target.checked)}
                    />
                    <span>{category}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Dietary Restrictions */}
            <div className="filter-group">
              <label className="filter-label">Dietary Preferences</label>
              <div className="checkbox-grid">
                {dietaryOptions.map(option => (
                  <label key={option} className="checkbox-item">
                    <input
                      type="checkbox"
                      checked={filters.dietaryRestrictions.includes(option)}
                      onChange={(e) => handleArrayFilterChange('dietaryRestrictions', option, e.target.checked)}
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Spice Level */}
            <div className="filter-group">
              <label className="filter-label">Spice Level</label>
              <select
                value={filters.spiceLevel}
                onChange={(e) => handleFilterChange('spiceLevel', e.target.value)}
                className="filter-select"
              >
                {spiceLevels.map(level => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Rating */}
            <div className="filter-group">
              <label className="filter-label">Minimum Rating</label>
              <div className="rating-filter">
                <div className="rating-buttons">
                  {[0, 1, 2, 3, 4, 5].map(rating => (
                    <button
                      key={rating}
                      onClick={() => handleFilterChange('rating', rating)}
                      className={`rating-btn ${filters.rating === rating ? 'active' : ''}`}
                    >
                      {rating === 0 ? 'Any' : `${rating}+ ⭐`}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Preparation Time */}
            <div className="filter-group">
              <label className="filter-label">Max Preparation Time</label>
              <div className="time-filter">
                <input
                  type="range"
                  min="5"
                  max="60"
                  step="5"
                  value={filters.preparationTime}
                  onChange={(e) => handleFilterChange('preparationTime', parseInt(e.target.value))}
                  className="range-slider"
                />
                <div className="time-display">
                  {filters.preparationTime} minutes
                </div>
              </div>
            </div>

            {/* Quick Filters */}
            <div className="filter-group">
              <label className="filter-label">Quick Filters</label>
              <div className="toggle-filters">
                <label className="toggle-item">
                  <input
                    type="checkbox"
                    checked={filters.isPopular}
                    onChange={(e) => handleFilterChange('isPopular', e.target.checked)}
                  />
                  <span>Popular Items</span>
                </label>
                <label className="toggle-item">
                  <input
                    type="checkbox"
                    checked={filters.isAvailable}
                    onChange={(e) => handleFilterChange('isAvailable', e.target.checked)}
                  />
                  <span>Available Only</span>
                </label>
              </div>
            </div>

            {/* Sort Options */}
            <div className="filter-group">
              <label className="filter-label">Sort By</label>
              <div className="sort-options">
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="filter-select"
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <button
                  onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="sort-order-btn"
                >
                  {filters.sortOrder === 'asc' ? '↑' : '↓'}
                </button>
              </div>
            </div>
          </div>

          <div className="filters-footer">
            <button onClick={toggleFilter} className="apply-filters">
              Apply Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilters;
