const express = require('express');
const { body } = require('express-validator');
const {
  createReview,
  getDishReviews,
  getUserReviews,
  updateReview,
  deleteReview,
  voteOnReview,
  getAllReviews,
  approveReview,
  respondToReview
} = require('../controllers/reviewController');
const { protect, admin } = require('../middleware/auth');
const { upload, handleUploadError } = require('../middleware/upload');

const router = express.Router();

// Validation rules
const reviewValidation = [
  body('dishId')
    .isMongoId()
    .withMessage('Invalid dish ID'),
  body('orderId')
    .isMongoId()
    .withMessage('Invalid order ID'),
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Title must be between 5 and 100 characters'),
  body('comment')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Comment must be between 10 and 500 characters')
];

const voteValidation = [
  body('voteType')
    .isIn(['helpful', 'not-helpful'])
    .withMessage('Vote type must be helpful or not-helpful')
];

const approveValidation = [
  body('isApproved')
    .isBoolean()
    .withMessage('isApproved must be a boolean')
];

const responseValidation = [
  body('message')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Response message must be between 10 and 500 characters')
];

// Public routes
router.get('/dish/:dishId', getDishReviews);

// Protected routes
router.post('/', 
  protect, 
  upload.array('images', 5), 
  handleUploadError,
  reviewValidation, 
  createReview
);

router.get('/my-reviews', protect, getUserReviews);
router.put('/:id', 
  protect, 
  upload.array('images', 5), 
  handleUploadError,
  updateReview
);
router.delete('/:id', protect, deleteReview);
router.post('/:id/vote', protect, voteValidation, voteOnReview);

// Admin routes
router.get('/admin/all', protect, admin, getAllReviews);
router.put('/:id/approve', protect, admin, approveValidation, approveReview);
router.post('/:id/respond', protect, admin, responseValidation, respondToReview);

module.exports = router;
