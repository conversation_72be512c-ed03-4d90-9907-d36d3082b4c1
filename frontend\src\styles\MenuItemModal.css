/* Menu Item Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  overflow-y: auto;
}

.menu-item-modal {
  background: white;
  border-radius: 16px;
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

/* Modal Content */
.modal-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 30px;
}

/* Image Gallery */
.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-image {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  aspect-ratio: 4/3;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.main-image:hover img {
  transform: scale(1.05);
}

.unavailable-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
}

.thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail:hover {
  border-color: #e74c3c;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.3);
}

/* Dish Info */
.dish-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dish-header h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 10px;
  line-height: 1.2;
}

.dish-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e74c3c;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-count {
  color: #666;
  font-size: 0.9rem;
}

.description {
  color: #555;
  line-height: 1.6;
  font-size: 1rem;
}

/* Dish Details */
.dish-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item .label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.detail-item .value {
  color: #555;
  font-size: 1rem;
}

.spice-level {
  color: #e74c3c;
  font-weight: 600;
}

/* Tags */
.dietary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dietary-tag {
  background: #27ae60;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.allergen-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.allergen-tag {
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Ingredients & Allergens */
.ingredients,
.allergens {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.ingredients h4,
.allergens h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1rem;
}

.ingredients p {
  color: #555;
  line-height: 1.5;
}

/* Order Section */
.order-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.quantity-selector label {
  font-weight: 600;
  color: #2c3e50;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background: white;
  border-radius: 8px;
  padding: 5px;
  border: 2px solid #e9ecef;
}

.quantity-controls button {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  width: 35px;
  height: 35px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.quantity-controls button:hover:not(:disabled) {
  background: #c0392b;
}

.quantity-controls button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.quantity {
  font-weight: 600;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.special-instructions {
  margin-bottom: 20px;
}

.special-instructions label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.special-instructions textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  font-size: 0.95rem;
}

.special-instructions textarea:focus {
  outline: none;
  border-color: #e74c3c;
}

.special-instructions small {
  color: #666;
  font-size: 0.8rem;
  margin-top: 5px;
  display: block;
}

.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 0;
  border-top: 2px solid #e9ecef;
}

.total-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
}

.total-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e74c3c;
}

.add-to-cart-btn {
  width: 100%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
}

.add-to-cart-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Modal Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 2px solid #e9ecef;
  background: #f8f9fa;
}

.tab {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.tab.active {
  color: #e74c3c;
  border-bottom-color: #e74c3c;
  background: white;
}

/* Tab Content */
.tab-content {
  padding: 30px;
  min-height: 200px;
}

.details-tab h3,
.nutrition-tab h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.chef-notes {
  margin-top: 25px;
  padding: 20px;
  background: #e8f4fd;
  border-left: 4px solid #3498db;
  border-radius: 0 8px 8px 0;
}

.chef-notes h4 {
  color: #2980b9;
  margin-bottom: 10px;
}

.chef-notes p {
  color: #2c3e50;
  line-height: 1.6;
  margin: 0;
}

.write-review-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 25px;
  transition: background 0.3s ease;
}

.write-review-btn:hover {
  background: #c0392b;
}

/* Nutrition Grid */
.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.nutrition-item .label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.nutrition-item .value {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e74c3c;
}

.no-nutrition {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }

  .dish-header h2 {
    font-size: 1.6rem;
  }

  .dish-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .dish-details {
    grid-template-columns: 1fr;
  }

  .modal-tabs {
    flex-direction: column;
  }

  .tab {
    text-align: left;
  }

  .nutrition-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 10px;
  }

  .menu-item-modal {
    max-height: 95vh;
  }

  .modal-content {
    padding: 15px;
  }

  .tab-content {
    padding: 20px;
  }

  .nutrition-grid {
    grid-template-columns: 1fr;
  }

  .quantity-controls {
    justify-content: center;
  }
}
