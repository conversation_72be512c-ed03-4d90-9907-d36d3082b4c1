/* Rating Component Styles */
.rating-component {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* Star Sizes */
.rating-star.small {
  font-size: 1rem;
}

.rating-star.medium {
  font-size: 1.2rem;
}

.rating-star.large {
  font-size: 1.5rem;
}

.rating-star.extra-large {
  font-size: 2rem;
}

/* Star Container */
.rating-star {
  position: relative;
  display: inline-block;
  transition: all 0.2s ease;
}

.rating-star.interactive:hover {
  transform: scale(1.1);
}

.star-empty {
  position: relative;
  z-index: 1;
}

.star-filled {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Half Star Container */
.rating-star-container {
  position: relative;
  display: inline-block;
}

.rating-star-container.small {
  width: 1rem;
  height: 1rem;
}

.rating-star-container.medium {
  width: 1.2rem;
  height: 1.2rem;
}

.rating-star-container.large {
  width: 1.5rem;
  height: 1.5rem;
}

.rating-star-container.extra-large {
  width: 2rem;
  height: 2rem;
}

.half-star {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  overflow: hidden;
  z-index: 3;
}

.full-star {
  position: absolute;
  top: 0;
  left: 50%;
  width: 50%;
  overflow: hidden;
  z-index: 2;
}

/* Rating Value */
.rating-value {
  font-weight: 600;
  color: #2c3e50;
  margin-left: 5px;
}

.rating-component.small .rating-value {
  font-size: 0.9rem;
}

.rating-component.medium .rating-value {
  font-size: 1rem;
}

.rating-component.large .rating-value {
  font-size: 1.1rem;
}

.rating-component.extra-large .rating-value {
  font-size: 1.3rem;
}

/* Interactive States */
.rating-component.interactive .rating-star {
  cursor: pointer;
  user-select: none;
}

.rating-component.readonly .rating-star {
  cursor: default;
}

/* Hover Effects */
.rating-star.interactive:hover {
  filter: brightness(1.1);
}

.rating-component.interactive:hover .rating-value {
  color: #e74c3c;
}

/* Star Rating Input */
.star-rating-input {
  margin-bottom: 20px;
}

.rating-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.rating-label.required {
  position: relative;
}

.required-asterisk {
  color: #e74c3c;
  margin-left: 4px;
  font-weight: bold;
}

.rating-error {
  display: block;
  margin-top: 5px;
  color: #e74c3c;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Animation for rating changes */
@keyframes ratingPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.rating-star.interactive:active {
  animation: ratingPulse 0.3s ease;
}

/* Focus states for accessibility */
.rating-star.interactive:focus {
  outline: 2px solid #e74c3c;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Color variations */
.rating-component.gold .star-filled {
  color: #ffd700 !important;
}

.rating-component.red .star-filled {
  color: #e74c3c !important;
}

.rating-component.blue .star-filled {
  color: #3498db !important;
}

.rating-component.green .star-filled {
  color: #27ae60 !important;
}

.rating-component.orange .star-filled {
  color: #f39c12 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rating-star.large {
    font-size: 1.3rem;
  }
  
  .rating-star.extra-large {
    font-size: 1.7rem;
  }
  
  .rating-stars {
    gap: 1px;
  }
}

@media (max-width: 480px) {
  .rating-star.medium {
    font-size: 1.1rem;
  }
  
  .rating-star.large {
    font-size: 1.2rem;
  }
  
  .rating-star.extra-large {
    font-size: 1.5rem;
  }
}

/* Loading state */
.rating-component.loading {
  opacity: 0.6;
  pointer-events: none;
}

.rating-component.loading .rating-star {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Disabled state */
.rating-component.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.rating-component.disabled .rating-star {
  cursor: not-allowed;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .star-empty {
    color: #000 !important;
  }
  
  .star-filled {
    color: #000 !important;
    background-color: #fff;
  }
  
  .rating-value {
    color: #000 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .rating-star,
  .star-filled {
    transition: none;
  }
  
  .rating-star.interactive:hover {
    transform: none;
  }
  
  .rating-star.interactive:active {
    animation: none;
  }
}
