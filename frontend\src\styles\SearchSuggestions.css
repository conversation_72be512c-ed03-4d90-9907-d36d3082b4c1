/* Search Suggestions Styles */
.search-suggestions {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 14px 50px 14px 16px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.search-input::placeholder {
  color: #95a5a6;
}

.search-icon {
  position: absolute;
  right: 16px;
  color: #666;
  font-size: 1.2rem;
  pointer-events: none;
}

.search-loading {
  position: absolute;
  right: 16px;
  font-size: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Suggestions Dropdown */
.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 5px;
}

.suggestions-section {
  padding: 8px 0;
}

.suggestions-section:not(:last-child) {
  border-bottom: 1px solid #f1f2f6;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clear-recent {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 0.8rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.clear-recent:hover {
  background: #fef5f5;
}

/* Suggestion Items */
.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background: #f8f9fa;
  border-left-color: #e74c3c;
}

.suggestion-item.selected {
  background: #fef5f5;
}

.suggestion-icon {
  font-size: 1.2rem;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.suggestion-text {
  font-size: 1rem;
  color: #2c3e50;
  font-weight: 500;
}

.suggestion-category {
  font-size: 0.85rem;
  color: #666;
}

.remove-recent {
  background: none;
  border: none;
  color: #999;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.remove-recent:hover {
  background: #f8d7da;
  color: #721c24;
}

/* Different suggestion types */
.suggestion-item.recent .suggestion-icon {
  color: #f39c12;
}

.suggestion-item.popular .suggestion-icon {
  color: #e74c3c;
}

.suggestion-item.suggestion .suggestion-icon {
  color: #3498db;
}

/* No suggestions */
.no-suggestions {
  padding: 20px 16px;
  text-align: center;
  color: #666;
}

.no-suggestions span {
  display: block;
  margin-bottom: 12px;
  font-size: 0.95rem;
}

.search-anyway {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.search-anyway:hover {
  background: #c0392b;
}

/* Scrollbar styling */
.suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.suggestions-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-suggestions {
    max-width: 100%;
  }

  .search-input {
    padding: 12px 45px 12px 14px;
    font-size: 0.95rem;
  }

  .suggestions-dropdown {
    max-height: 300px;
  }

  .suggestion-item {
    padding: 10px 14px;
  }

  .suggestion-text {
    font-size: 0.95rem;
  }

  .suggestion-category {
    font-size: 0.8rem;
  }

  .section-header {
    padding: 6px 14px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .search-input {
    padding: 10px 40px 10px 12px;
    font-size: 0.9rem;
  }

  .search-icon {
    right: 12px;
    font-size: 1.1rem;
  }

  .suggestion-item {
    padding: 8px 12px;
  }

  .suggestion-icon {
    font-size: 1.1rem;
    margin-right: 10px;
  }

  .suggestion-text {
    font-size: 0.9rem;
  }

  .no-suggestions {
    padding: 16px 12px;
  }
}

/* Animation for dropdown appearance */
.suggestions-dropdown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus and accessibility */
.suggestion-item:focus {
  outline: 2px solid #e74c3c;
  outline-offset: -2px;
}

.search-input:focus + .search-icon {
  color: #e74c3c;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .search-input {
    border-color: #000;
  }

  .suggestion-item:hover,
  .suggestion-item.selected {
    background: #000;
    color: #fff;
  }

  .suggestion-item:hover .suggestion-text,
  .suggestion-item.selected .suggestion-text {
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .search-input,
  .suggestion-item,
  .clear-recent,
  .remove-recent,
  .search-anyway {
    transition: none;
  }

  .suggestions-dropdown {
    animation: none;
  }

  .search-loading {
    animation: none;
  }
}
