[{"D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\index.js": "1", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\App.js": "2", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\reportWebVitals.js": "3", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Home.js": "4", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\AuthContext.js": "5", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\CartContext.js": "6", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Reservations.js": "7", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Menu.js": "8", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Login.js": "9", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Profile.js": "10", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Register.js": "11", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Contact.js": "12", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Orders.js": "13", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Footer.js": "14", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\ProtectedRoute.js": "15", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Navbar.js": "16", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\OrderManagement.js": "17", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\MenuManagement.js": "18", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\Dashboard.js": "19", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ContactManagement.js": "20", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ReservationManagement.js": "21", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Cart.js": "22", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\BillSplit.js": "23", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\BillSplitPage.js": "24", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\LoyaltyDashboard.js": "25"}, {"size": 535, "mtime": 1748922294881, "results": "26", "hashOfConfig": "27"}, {"size": 4178, "mtime": 1748927790955, "results": "28", "hashOfConfig": "27"}, {"size": 362, "mtime": 1748922295292, "results": "29", "hashOfConfig": "27"}, {"size": 5829, "mtime": 1748922548807, "results": "30", "hashOfConfig": "27"}, {"size": 4735, "mtime": 1748922463633, "results": "31", "hashOfConfig": "27"}, {"size": 4038, "mtime": 1748922479043, "results": "32", "hashOfConfig": "27"}, {"size": 6709, "mtime": 1748922668933, "results": "33", "hashOfConfig": "27"}, {"size": 8892, "mtime": 1748922575495, "results": "34", "hashOfConfig": "27"}, {"size": 4167, "mtime": 1748922589856, "results": "35", "hashOfConfig": "27"}, {"size": 4241, "mtime": 1748922700168, "results": "36", "hashOfConfig": "27"}, {"size": 8058, "mtime": 1748922611812, "results": "37", "hashOfConfig": "27"}, {"size": 5040, "mtime": 1748922685415, "results": "38", "hashOfConfig": "27"}, {"size": 4920, "mtime": 1748925986846, "results": "39", "hashOfConfig": "27"}, {"size": 2130, "mtime": 1748922501061, "results": "40", "hashOfConfig": "27"}, {"size": 617, "mtime": 1748922507062, "results": "41", "hashOfConfig": "27"}, {"size": 3284, "mtime": 1748927762325, "results": "42", "hashOfConfig": "27"}, {"size": 9955, "mtime": 1748923154750, "results": "43", "hashOfConfig": "27"}, {"size": 11776, "mtime": 1748923126375, "results": "44", "hashOfConfig": "27"}, {"size": 4691, "mtime": 1748922731563, "results": "45", "hashOfConfig": "27"}, {"size": 12323, "mtime": 1748923210709, "results": "46", "hashOfConfig": "27"}, {"size": 10586, "mtime": 1748923181786, "results": "47", "hashOfConfig": "27"}, {"size": 8555, "mtime": 1748922527834, "results": "48", "hashOfConfig": "27"}, {"size": 13523, "mtime": 1748925907721, "results": "49", "hashOfConfig": "27"}, {"size": 10562, "mtime": 1748925939105, "results": "50", "hashOfConfig": "27"}, {"size": 14480, "mtime": 1748927729953, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "113lfpm", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\index.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\App.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\reportWebVitals.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Home.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\AuthContext.js", ["127"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\CartContext.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Reservations.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Menu.js", ["128"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Login.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Profile.js", ["129"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Register.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Contact.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Orders.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Footer.js", ["130", "131", "132", "133"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Navbar.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\OrderManagement.js", ["134"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\MenuManagement.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\Dashboard.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ContactManagement.js", ["135"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ReservationManagement.js", ["136"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Cart.js", ["137"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\BillSplit.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\BillSplitPage.js", ["138", "139", "140"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\LoyaltyDashboard.js", [], [], {"ruleId": "141", "severity": 1, "message": "142", "line": 178, "column": 6, "nodeType": "143", "endLine": 178, "endColumn": 8, "suggestions": "144"}, {"ruleId": "141", "severity": 1, "message": "145", "line": 35, "column": 6, "nodeType": "143", "endLine": 35, "endColumn": 32, "suggestions": "146"}, {"ruleId": "147", "severity": 1, "message": "148", "line": 43, "column": 11, "nodeType": "149", "messageId": "150", "endLine": 43, "endColumn": 17}, {"ruleId": "151", "severity": 1, "message": "152", "line": 13, "column": 13, "nodeType": "153", "endLine": 13, "endColumn": 47}, {"ruleId": "151", "severity": 1, "message": "152", "line": 14, "column": 13, "nodeType": "153", "endLine": 14, "endColumn": 48}, {"ruleId": "151", "severity": 1, "message": "152", "line": 15, "column": 13, "nodeType": "153", "endLine": 15, "endColumn": 46}, {"ruleId": "151", "severity": 1, "message": "152", "line": 16, "column": 13, "nodeType": "153", "endLine": 16, "endColumn": 46}, {"ruleId": "141", "severity": 1, "message": "154", "line": 16, "column": 6, "nodeType": "143", "endLine": 16, "endColumn": 15, "suggestions": "155"}, {"ruleId": "141", "severity": 1, "message": "156", "line": 18, "column": 6, "nodeType": "143", "endLine": 18, "endColumn": 15, "suggestions": "157"}, {"ruleId": "141", "severity": 1, "message": "158", "line": 16, "column": 6, "nodeType": "143", "endLine": 16, "endColumn": 15, "suggestions": "159"}, {"ruleId": "147", "severity": 1, "message": "160", "line": 70, "column": 13, "nodeType": "149", "messageId": "150", "endLine": 70, "endColumn": 21}, {"ruleId": "147", "severity": 1, "message": "161", "line": 11, "column": 10, "nodeType": "149", "messageId": "150", "endLine": 11, "endColumn": 29}, {"ruleId": "147", "severity": 1, "message": "162", "line": 11, "column": 31, "nodeType": "149", "messageId": "150", "endLine": 11, "endColumn": 53}, {"ruleId": "141", "severity": 1, "message": "163", "line": 16, "column": 6, "nodeType": "143", "endLine": 16, "endColumn": 15, "suggestions": "164"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUser'. Either include it or remove the dependency array.", "ArrayExpression", ["165"], "React Hook useEffect has a missing dependency: 'fetchDishes'. Either include it or remove the dependency array.", ["166"], "no-unused-vars", "'result' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["167"], "React Hook useEffect has a missing dependency: 'fetchContacts'. Either include it or remove the dependency array.", ["168"], "React Hook useEffect has a missing dependency: 'fetchReservations'. Either include it or remove the dependency array.", ["169"], "'response' is assigned a value but never used.", "'selectedParticipant' is assigned a value but never used.", "'setSelectedParticipant' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBillSplitData'. Either include it or remove the dependency array.", ["170"], {"desc": "171", "fix": "172"}, {"desc": "173", "fix": "174"}, {"desc": "175", "fix": "176"}, {"desc": "177", "fix": "178"}, {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, "Update the dependencies array to be: [loadUser]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [fetchDishes, filters, pagination.page]", {"range": "185", "text": "186"}, "Update the dependencies array to be: [fetchOrders, filters]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [fetchContacts, filters]", {"range": "189", "text": "190"}, "Update the dependencies array to be: [fetchReservations, filters]", {"range": "191", "text": "192"}, "Update the dependencies array to be: [fetchBillSplitData, orderId]", {"range": "193", "text": "194"}, [4282, 4284], "[loadUser]", [806, 832], "[fetchDishes, filters, pagination.page]", [438, 447], "[fetchOrders, filters]", [566, 575], "[fetchContacts, filters]", [466, 475], "[fetchReservations, filters]", [553, 562], "[fetchBillSplitData, orderId]"]