/* Loading Spinner Styles */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-spinner.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9999;
}

.loading-spinner.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 100;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

/* Spinner Sizes */
.loading-spinner.small .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner.medium .spinner {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.loading-spinner.large .spinner {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

/* Spinner Animation */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #e74c3c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.loading-spinner.small .loading-text {
  font-size: 0.9rem;
}

.loading-spinner.large .loading-text {
  font-size: 1.1rem;
}

/* Skeleton Card */
.skeleton-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-image {
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-title {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  width: 80%;
}

.skeleton-text {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  width: 100%;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.skeleton-price {
  height: 18px;
  width: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-rating {
  height: 16px;
  width: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

/* Skeleton List */
.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.skeleton-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  flex-shrink: 0;
}

.skeleton-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Dots Loader */
.dots-loader {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dots-loader.small .dot {
  width: 6px;
  height: 6px;
}

.dots-loader.medium .dot {
  width: 8px;
  height: 8px;
}

.dots-loader.large .dot {
  width: 12px;
  height: 12px;
}

.dot {
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse Loader */
.pulse-loader {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pulse-loader.small .pulse {
  width: 8px;
  height: 8px;
}

.pulse-loader.medium .pulse {
  width: 12px;
  height: 12px;
}

.pulse-loader.large .pulse {
  width: 16px;
  height: 16px;
}

.pulse {
  border-radius: 50%;
  animation: pulseBeat 1.2s ease-in-out infinite;
}

@keyframes pulseBeat {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  40% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Button Loader */
.button-loader {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button-loader.small .button-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.button-loader.medium .button-spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.button-loader.large .button-spinner {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

.button-spinner {
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skeleton-card {
    margin: 0 -10px;
  }
  
  .skeleton-content {
    padding: 15px;
  }
  
  .skeleton-list-item {
    padding: 12px;
  }
  
  .skeleton-avatar {
    width: 40px;
    height: 40px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .spinner,
  .button-spinner {
    animation: none;
    border-top-color: transparent;
    border-right-color: currentColor;
  }
  
  .skeleton-image,
  .skeleton-title,
  .skeleton-text,
  .skeleton-price,
  .skeleton-rating,
  .skeleton-avatar {
    animation: none;
    background: #f0f0f0;
  }
  
  .dot,
  .pulse {
    animation: none;
  }
}
