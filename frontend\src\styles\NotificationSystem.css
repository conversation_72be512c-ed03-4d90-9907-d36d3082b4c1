/* Notification System Styles */
.notification-bell-container {
  position: relative;
}

.notification-bell {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  position: relative;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-bell:hover {
  background: rgba(231, 76, 60, 0.1);
  transform: scale(1.1);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Notification Dropdown */
.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.notification-header h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.notification-controls {
  display: flex;
  gap: 10px;
}

.mark-all-read,
.clear-all {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.mark-all-read:hover,
.clear-all:hover {
  background: rgba(231, 76, 60, 0.1);
}

/* Notification List */
.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.no-notifications {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.no-notifications span {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
  opacity: 0.5;
}

.no-notifications p {
  margin: 0;
  font-size: 1rem;
}

/* Notification Item */
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #f1f2f6;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background: #f8f9fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #fef5f5;
  border-left: 4px solid #e74c3c;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 8px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
}

/* Priority Indicators */
.notification-item.priority-high {
  border-left-color: #e74c3c;
}

.notification-item.priority-medium {
  border-left-color: #f39c12;
}

.notification-item.priority-low {
  border-left-color: #3498db;
}

.notification-icon {
  font-size: 1.5rem;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.notification-content h4 {
  margin: 0;
  font-size: 1rem;
  color: #2c3e50;
  font-weight: 600;
}

.notification-time {
  font-size: 0.8rem;
  color: #999;
  white-space: nowrap;
  margin-left: 10px;
}

.notification-content p {
  margin: 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-close {
  background: none;
  border: none;
  color: #999;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 8px;
  flex-shrink: 0;
}

.notification-close:hover {
  background: #f8d7da;
  color: #721c24;
}

/* Notification Actions */
.notification-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: #e74c3c;
  color: white;
}

.action-btn.primary:hover {
  background: #c0392b;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 320px;
    right: -20px;
  }

  .notification-item {
    padding: 12px 16px;
  }

  .notification-content h4 {
    font-size: 0.95rem;
  }

  .notification-content p {
    font-size: 0.85rem;
  }

  .notification-header {
    padding: 16px;
  }

  .notification-header h3 {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    width: 280px;
    right: -40px;
  }

  .notification-item {
    padding: 10px 12px;
  }

  .notification-icon {
    font-size: 1.3rem;
    margin-right: 10px;
  }

  .notification-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}

/* Animation for new notifications */
.notification-item {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .notification-dropdown {
    border-color: #000;
  }

  .notification-item {
    border-bottom-color: #000;
  }

  .notification-item:hover {
    background: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .notification-badge {
    animation: none;
  }

  .notification-dropdown {
    animation: none;
  }

  .notification-item {
    animation: none;
  }

  .notification-bell {
    transition: none;
  }

  .notification-bell:hover {
    transform: none;
  }
}
