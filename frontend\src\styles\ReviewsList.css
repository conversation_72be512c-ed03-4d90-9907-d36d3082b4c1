/* Reviews Section Styles */
.reviews-section {
  margin-top: 40px;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.reviews-header {
  margin-bottom: 30px;
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 20px;
}

.reviews-header h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 20px;
}

/* Rating Summary */
.rating-summary {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.average-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.rating-number {
  font-size: 3rem;
  font-weight: bold;
  color: #e74c3c;
  line-height: 1;
}

.rating-stars {
  margin: 10px 0;
}

.star {
  font-size: 1.2rem;
  margin: 0 2px;
  color: #ddd;
}

.star.filled {
  color: #ffd700;
}

.total-reviews {
  color: #666;
  font-size: 0.9rem;
}

/* Rating Breakdown */
.rating-breakdown {
  flex: 1;
  max-width: 300px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.rating-label {
  min-width: 50px;
  font-size: 0.9rem;
  color: #666;
}

.bar-container {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
  transition: width 0.3s ease;
}

.rating-count {
  min-width: 30px;
  font-size: 0.8rem;
  color: #999;
}

/* Reviews Filters */
.reviews-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.reviews-filters .filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reviews-filters label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.reviews-filters select {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
}

/* Reviews List */
.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.no-reviews {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-reviews p {
  margin-bottom: 10px;
  font-size: 1.1rem;
}

/* Review Item */
.review-item {
  padding: 25px;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  background: #fafafa;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.review-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.reviewer-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reviewer-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.review-date {
  color: #666;
  font-size: 0.9rem;
}

.verified-badge {
  background: #27ae60;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.review-rating {
  display: flex;
  gap: 2px;
}

/* Review Content */
.review-content {
  margin-bottom: 20px;
}

.review-title {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 600;
}

.review-comment {
  color: #555;
  line-height: 1.6;
  margin-bottom: 15px;
}

/* Review Images */
.review-images {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.review-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 2px solid #ddd;
}

.review-image:hover {
  transform: scale(1.05);
  border-color: #e74c3c;
}

/* Review Actions */
.review-actions {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
}

.helpfulness {
  display: flex;
  align-items: center;
  gap: 15px;
}

.helpfulness span {
  color: #666;
  font-size: 0.9rem;
}

.vote-btn {
  background: none;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.vote-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #e74c3c;
}

.vote-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.vote-btn.helpful:hover:not(:disabled) {
  background: #d4edda;
  border-color: #27ae60;
  color: #27ae60;
}

.vote-btn.not-helpful:hover:not(:disabled) {
  background: #f8d7da;
  border-color: #dc3545;
  color: #dc3545;
}

/* Admin Response */
.admin-response {
  margin-top: 15px;
  padding: 15px;
  background: #e8f4fd;
  border-left: 4px solid #3498db;
  border-radius: 0 8px 8px 0;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.response-header strong {
  color: #2980b9;
}

.response-date {
  color: #666;
  font-size: 0.8rem;
}

.response-message {
  color: #2c3e50;
  line-height: 1.5;
  margin: 0;
}

/* Pagination */
.reviews-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.pagination-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #c0392b;
}

.pagination-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.pagination-info {
  color: #666;
  font-weight: 500;
}

/* Loading State */
.reviews-loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reviews-section {
    padding: 20px;
    margin-top: 20px;
  }

  .rating-summary {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  .rating-breakdown {
    max-width: 100%;
  }

  .reviews-filters {
    flex-direction: column;
    gap: 15px;
  }

  .review-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .review-images {
    justify-content: center;
  }

  .helpfulness {
    flex-wrap: wrap;
    gap: 10px;
  }

  .reviews-pagination {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .reviews-section {
    padding: 15px;
  }

  .reviews-header h3 {
    font-size: 1.5rem;
  }

  .rating-number {
    font-size: 2.5rem;
  }

  .review-item {
    padding: 20px;
  }

  .review-title {
    font-size: 1.1rem;
  }

  .vote-btn {
    padding: 5px 10px;
    font-size: 0.8rem;
  }
}
