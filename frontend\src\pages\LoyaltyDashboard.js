import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useAuth } from '../context/AuthContext';
import '../styles/LoyaltyDashboard.css';

const LoyaltyDashboard = () => {
  const [loyaltyData, setLoyaltyData] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [redeemAmount, setRedeemAmount] = useState(100);
  const [referralCode, setReferralCode] = useState('');

  const { user } = useAuth();

  useEffect(() => {
    fetchLoyaltyData();
    fetchTransactions();
    fetchLeaderboard();
  }, []);

  const fetchLoyaltyData = async () => {
    try {
      const response = await axios.get('/api/loyalty/profile');
      setLoyaltyData(response.data);
    } catch (error) {
      toast.error('Failed to load loyalty data');
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactions = async () => {
    try {
      const response = await axios.get('/api/loyalty/transactions');
      setTransactions(response.data.transactions);
    } catch (error) {
      console.error('Failed to load transactions');
    }
  };

  const fetchLeaderboard = async () => {
    try {
      const response = await axios.get('/api/loyalty/leaderboard');
      setLeaderboard(response.data);
    } catch (error) {
      console.error('Failed to load leaderboard');
    }
  };

  const handleRedeem = async () => {
    try {
      const response = await axios.post('/api/loyalty/redeem', {
        points: redeemAmount,
        rewardType: 'discount'
      });
      
      toast.success(`Redeemed ${redeemAmount} points for $${response.data.discountValue} discount!`);
      toast.info(`Discount code: ${response.data.discountCode}`);
      
      fetchLoyaltyData();
      fetchTransactions();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to redeem points');
    }
  };

  const handleApplyReferral = async () => {
    try {
      await axios.post('/api/loyalty/referral', { referralCode });
      toast.success('Referral code applied successfully!');
      setReferralCode('');
      fetchLoyaltyData();
      fetchTransactions();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to apply referral code');
    }
  };

  const copyReferralCode = () => {
    if (loyaltyData?.loyaltyProgram?.referralCode) {
      navigator.clipboard.writeText(loyaltyData.loyaltyProgram.referralCode);
      toast.success('Referral code copied to clipboard!');
    }
  };

  const getTierColor = (tier) => {
    const colors = {
      bronze: '#CD7F32',
      silver: '#C0C0C0',
      gold: '#FFD700',
      platinum: '#E5E4E2'
    };
    return colors[tier] || colors.bronze;
  };

  const getTierIcon = (tier) => {
    const icons = {
      bronze: '🥉',
      silver: '🥈',
      gold: '🥇',
      platinum: '💎'
    };
    return icons[tier] || icons.bronze;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return <div className="loading">Loading loyalty dashboard...</div>;
  }

  if (!loyaltyData) {
    return <div className="error">Failed to load loyalty data</div>;
  }

  const { loyaltyProgram, tierBenefits, pointsExpiringSoon } = loyaltyData;

  return (
    <div className="loyalty-dashboard">
      <div className="container">
        <div className="loyalty-header">
          <h1>Loyalty Rewards</h1>
          <p>Earn points, unlock rewards, and enjoy exclusive benefits!</p>
        </div>

        {/* Loyalty Overview Card */}
        <div className="loyalty-overview">
          <div className="tier-card">
            <div className="tier-info">
              <span className="tier-icon">{getTierIcon(loyaltyProgram.tier)}</span>
              <div className="tier-details">
                <h2 style={{ color: getTierColor(loyaltyProgram.tier) }}>
                  {loyaltyProgram.tier.toUpperCase()} MEMBER
                </h2>
                <p>{user.name}</p>
              </div>
            </div>
            <div className="points-summary">
              <div className="points-item">
                <span className="points-value">{loyaltyProgram.availablePoints}</span>
                <span className="points-label">Available Points</span>
              </div>
              <div className="points-item">
                <span className="points-value">{loyaltyProgram.lifetimePoints}</span>
                <span className="points-label">Lifetime Points</span>
              </div>
            </div>
          </div>

          {/* Tier Progress */}
          {loyaltyProgram.tierProgress.nextTier && (
            <div className="tier-progress">
              <h3>Progress to {loyaltyProgram.tierProgress.nextTier.toUpperCase()}</h3>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ 
                    width: `${(loyaltyProgram.tierProgress.currentSpend / loyaltyProgram.tierProgress.nextTierSpend) * 100}%`,
                    backgroundColor: getTierColor(loyaltyProgram.tierProgress.nextTier)
                  }}
                ></div>
              </div>
              <p>
                ${loyaltyProgram.tierProgress.currentSpend} / ${loyaltyProgram.tierProgress.nextTierSpend}
              </p>
            </div>
          )}

          {/* Tier Benefits */}
          <div className="tier-benefits">
            <h3>Your Benefits</h3>
            <div className="benefits-grid">
              <div className="benefit-item">
                <span className="benefit-icon">⭐</span>
                <span>{tierBenefits.pointsMultiplier}x Points</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">💰</span>
                <span>{tierBenefits.discountPercentage}% Discount</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">🚚</span>
                <span>{tierBenefits.freeDelivery ? 'Free Delivery' : 'Paid Delivery'}</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">🎂</span>
                <span>{tierBenefits.birthdayBonus} Birthday Points</span>
              </div>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {pointsExpiringSoon > 0 && (
          <div className="alert alert-warning">
            ⚠️ You have {pointsExpiringSoon} points expiring in the next 30 days!
          </div>
        )}

        {/* Tabs */}
        <div className="loyalty-tabs">
          <button 
            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button 
            className={`tab ${activeTab === 'redeem' ? 'active' : ''}`}
            onClick={() => setActiveTab('redeem')}
          >
            Redeem Points
          </button>
          <button 
            className={`tab ${activeTab === 'referral' ? 'active' : ''}`}
            onClick={() => setActiveTab('referral')}
          >
            Referrals
          </button>
          <button 
            className={`tab ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            History
          </button>
          <button 
            className={`tab ${activeTab === 'leaderboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('leaderboard')}
          >
            Leaderboard
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'overview' && (
            <div className="overview-content">
              <div className="achievements-section">
                <h3>Recent Achievements</h3>
                {loyaltyProgram.achievements.length > 0 ? (
                  <div className="achievements-grid">
                    {loyaltyProgram.achievements.slice(-3).map((achievement, index) => (
                      <div key={index} className="achievement-card">
                        <h4>{achievement.title}</h4>
                        <p>{achievement.description}</p>
                        <span className="achievement-points">+{achievement.pointsAwarded} points</span>
                        <span className="achievement-date">{formatDate(achievement.unlockedAt)}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p>No achievements yet. Start ordering to unlock achievements!</p>
                )}
              </div>
            </div>
          )}

          {activeTab === 'redeem' && (
            <div className="redeem-content">
              <h3>Redeem Your Points</h3>
              <div className="redeem-section">
                <div className="redeem-calculator">
                  <label>Points to Redeem (Min: 100):</label>
                  <input
                    type="number"
                    min="100"
                    step="50"
                    value={redeemAmount}
                    onChange={(e) => setRedeemAmount(parseInt(e.target.value))}
                  />
                  <p>Discount Value: ${(redeemAmount * 0.01).toFixed(2)}</p>
                  <button 
                    className="btn btn-primary"
                    onClick={handleRedeem}
                    disabled={redeemAmount < 100 || redeemAmount > loyaltyProgram.availablePoints}
                  >
                    Redeem Points
                  </button>
                </div>
                
                <div className="redeem-info">
                  <h4>How it works:</h4>
                  <ul>
                    <li>100 points = $1.00 discount</li>
                    <li>Minimum redemption: 100 points</li>
                    <li>Discount codes are valid for 30 days</li>
                    <li>Use discount codes at checkout</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'referral' && (
            <div className="referral-content">
              <div className="referral-section">
                <div className="my-referral">
                  <h3>Your Referral Code</h3>
                  <div className="referral-code-display">
                    <span className="referral-code">{loyaltyProgram.referralCode}</span>
                    <button className="copy-btn" onClick={copyReferralCode}>
                      Copy
                    </button>
                  </div>
                  <p>Share this code with friends and both of you get 100 points!</p>
                  
                  {loyaltyProgram.referrals.length > 0 && (
                    <div className="referral-stats">
                      <h4>Your Referrals ({loyaltyProgram.referrals.length})</h4>
                      <div className="referral-list">
                        {loyaltyProgram.referrals.map((referral, index) => (
                          <div key={index} className="referral-item">
                            <span>{referral.user.name}</span>
                            <span>+{referral.pointsAwarded} points</span>
                            <span>{formatDate(referral.createdAt)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {!loyaltyProgram.referredBy && (
                  <div className="apply-referral">
                    <h3>Have a Referral Code?</h3>
                    <div className="referral-input">
                      <input
                        type="text"
                        placeholder="Enter referral code"
                        value={referralCode}
                        onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                      />
                      <button 
                        className="btn btn-primary"
                        onClick={handleApplyReferral}
                        disabled={!referralCode}
                      >
                        Apply Code
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="history-content">
              <h3>Points History</h3>
              <div className="transactions-list">
                {transactions.map((transaction, index) => (
                  <div key={index} className={`transaction-item ${transaction.type}`}>
                    <div className="transaction-info">
                      <span className="transaction-description">{transaction.description}</span>
                      <span className="transaction-date">{formatDate(transaction.createdAt)}</span>
                    </div>
                    <span className={`transaction-points ${transaction.type}`}>
                      {transaction.type === 'redeemed' ? '-' : '+'}{Math.abs(transaction.points)} pts
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'leaderboard' && (
            <div className="leaderboard-content">
              <h3>Top Members</h3>
              <div className="leaderboard-list">
                {leaderboard.map((member, index) => (
                  <div key={index} className="leaderboard-item">
                    <span className="rank">#{index + 1}</span>
                    <span className="member-name">{member.customer.name}</span>
                    <span className="member-tier">{getTierIcon(member.tier)}</span>
                    <span className="member-points">{member.lifetimePoints} pts</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoyaltyDashboard;
