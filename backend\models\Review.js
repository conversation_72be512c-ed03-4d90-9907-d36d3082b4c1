const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  dish: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dish',
    required: true
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  rating: {
    type: Number,
    required: [true, 'Please provide a rating'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot be more than 5']
  },
  title: {
    type: String,
    required: [true, 'Please provide a review title'],
    trim: true,
    maxlength: [100, 'Review title cannot be more than 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Please provide a review comment'],
    maxlength: [500, 'Review comment cannot be more than 500 characters']
  },
  images: [{
    type: String // Store image filenames
  }],
  isVerifiedPurchase: {
    type: Boolean,
    default: true
  },
  helpfulVotes: {
    type: Number,
    default: 0
  },
  votedBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    voteType: {
      type: String,
      enum: ['helpful', 'not-helpful']
    }
  }],
  isApproved: {
    type: Boolean,
    default: true
  },
  adminResponse: {
    message: String,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    respondedAt: Date
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
reviewSchema.index({ dish: 1, createdAt: -1 });
reviewSchema.index({ customer: 1, createdAt: -1 });
reviewSchema.index({ rating: -1 });
reviewSchema.index({ isApproved: 1 });

// Ensure one review per customer per dish per order
reviewSchema.index({ customer: 1, dish: 1, order: 1 }, { unique: true });

// Calculate average rating for dish after save/remove
reviewSchema.statics.calculateAverageRating = async function(dishId) {
  const stats = await this.aggregate([
    {
      $match: { dish: dishId, isApproved: true }
    },
    {
      $group: {
        _id: '$dish',
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 }
      }
    }
  ]);

  try {
    if (stats.length > 0) {
      await this.model('Dish').findByIdAndUpdate(dishId, {
        rating: Math.round(stats[0].averageRating * 10) / 10,
        reviewCount: stats[0].totalReviews
      });
    } else {
      await this.model('Dish').findByIdAndUpdate(dishId, {
        rating: 0,
        reviewCount: 0
      });
    }
  } catch (error) {
    console.error('Error updating dish rating:', error);
  }
};

// Update dish rating after saving review
reviewSchema.post('save', function() {
  this.constructor.calculateAverageRating(this.dish);
});

// Update dish rating after removing review
reviewSchema.post('remove', function() {
  this.constructor.calculateAverageRating(this.dish);
});

module.exports = mongoose.model('Review', reviewSchema);
