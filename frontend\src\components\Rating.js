import React, { useState } from 'react';
import '../styles/Rating.css';

const Rating = ({ 
  value = 0, 
  onChange, 
  readonly = false, 
  size = 'medium',
  showValue = false,
  precision = 1,
  color = '#ffd700',
  emptyColor = '#ddd',
  hoverColor = '#ffed4e'
}) => {
  const [hoverValue, setHoverValue] = useState(0);
  const [isHovering, setIsHovering] = useState(false);

  const handleMouseEnter = (starValue) => {
    if (!readonly) {
      setHoverValue(starValue);
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverValue(0);
      setIsHovering(false);
    }
  };

  const handleClick = (starValue) => {
    if (!readonly && onChange) {
      onChange(starValue);
    }
  };

  const getStarValue = (starIndex) => {
    if (precision === 0.5) {
      return starIndex + 0.5;
    }
    return starIndex + 1;
  };

  const getStarFill = (starIndex) => {
    const starValue = getStarValue(starIndex);
    const currentValue = isHovering ? hoverValue : value;
    
    if (currentValue >= starValue) {
      return 100;
    } else if (currentValue > starIndex && currentValue < starValue) {
      return ((currentValue - starIndex) * 100);
    }
    return 0;
  };

  const renderStar = (starIndex) => {
    const fillPercentage = getStarFill(starIndex);
    const starValue = getStarValue(starIndex);
    
    return (
      <div
        key={starIndex}
        className={`rating-star ${size} ${!readonly ? 'interactive' : ''}`}
        onMouseEnter={() => handleMouseEnter(starValue)}
        onMouseLeave={handleMouseLeave}
        onClick={() => handleClick(starValue)}
        style={{
          cursor: readonly ? 'default' : 'pointer'
        }}
      >
        <div 
          className="star-empty"
          style={{ color: emptyColor }}
        >
          ★
        </div>
        <div 
          className="star-filled"
          style={{ 
            width: `${fillPercentage}%`,
            color: isHovering ? hoverColor : color
          }}
        >
          ★
        </div>
      </div>
    );
  };

  const renderHalfStar = (starIndex) => {
    const fullStarValue = starIndex + 1;
    const halfStarValue = starIndex + 0.5;
    const currentValue = isHovering ? hoverValue : value;
    
    return (
      <div key={starIndex} className={`rating-star-container ${size}`}>
        {/* Half star */}
        <div
          className={`rating-star half-star ${size} ${!readonly ? 'interactive' : ''}`}
          onMouseEnter={() => handleMouseEnter(halfStarValue)}
          onMouseLeave={handleMouseLeave}
          onClick={() => handleClick(halfStarValue)}
          style={{
            cursor: readonly ? 'default' : 'pointer'
          }}
        >
          <div 
            className="star-empty"
            style={{ color: emptyColor }}
          >
            ★
          </div>
          <div 
            className="star-filled"
            style={{ 
              width: currentValue >= halfStarValue ? '50%' : '0%',
              color: isHovering ? hoverColor : color
            }}
          >
            ★
          </div>
        </div>
        
        {/* Full star */}
        <div
          className={`rating-star full-star ${size} ${!readonly ? 'interactive' : ''}`}
          onMouseEnter={() => handleMouseEnter(fullStarValue)}
          onMouseLeave={handleMouseLeave}
          onClick={() => handleClick(fullStarValue)}
          style={{
            cursor: readonly ? 'default' : 'pointer'
          }}
        >
          <div 
            className="star-empty"
            style={{ color: emptyColor }}
          >
            ★
          </div>
          <div 
            className="star-filled"
            style={{ 
              width: currentValue >= fullStarValue ? '100%' : '0%',
              color: isHovering ? hoverColor : color
            }}
          >
            ★
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`rating-component ${size} ${readonly ? 'readonly' : 'interactive'}`}>
      <div className="rating-stars">
        {precision === 0.5 ? (
          // Render half-star precision
          Array.from({ length: 5 }, (_, index) => renderHalfStar(index))
        ) : (
          // Render full-star precision
          Array.from({ length: 5 }, (_, index) => renderStar(index))
        )}
      </div>
      
      {showValue && (
        <span className="rating-value">
          {value.toFixed(1)}
        </span>
      )}
    </div>
  );
};

// Quick rating display component for readonly use
export const QuickRating = ({ value, size = 'small', showValue = true }) => {
  return (
    <Rating 
      value={value} 
      readonly={true} 
      size={size} 
      showValue={showValue}
    />
  );
};

// Star rating input component for forms
export const StarRatingInput = ({ 
  value, 
  onChange, 
  label, 
  required = false,
  error = null 
}) => {
  return (
    <div className="star-rating-input">
      {label && (
        <label className={`rating-label ${required ? 'required' : ''}`}>
          {label}
          {required && <span className="required-asterisk">*</span>}
        </label>
      )}
      <Rating 
        value={value} 
        onChange={onChange} 
        size="large"
        showValue={true}
      />
      {error && <span className="rating-error">{error}</span>}
    </div>
  );
};

export default Rating;
