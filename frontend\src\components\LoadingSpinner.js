import React from 'react';
import '../styles/LoadingSpinner.css';

const LoadingSpinner = ({ 
  size = 'medium', 
  color = '#e74c3c', 
  text = 'Loading...', 
  showText = true,
  fullScreen = false,
  overlay = false 
}) => {
  const spinnerClass = `loading-spinner ${size} ${fullScreen ? 'fullscreen' : ''} ${overlay ? 'overlay' : ''}`;
  
  return (
    <div className={spinnerClass}>
      <div className="spinner-container">
        <div 
          className="spinner" 
          style={{ borderTopColor: color }}
        ></div>
        {showText && <p className="loading-text">{text}</p>}
      </div>
    </div>
  );
};

// Skeleton loader for cards
export const SkeletonCard = ({ count = 1 }) => {
  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className="skeleton-card">
          <div className="skeleton-image"></div>
          <div className="skeleton-content">
            <div className="skeleton-title"></div>
            <div className="skeleton-text"></div>
            <div className="skeleton-text short"></div>
            <div className="skeleton-meta">
              <div className="skeleton-price"></div>
              <div className="skeleton-rating"></div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

// Skeleton loader for list items
export const SkeletonList = ({ count = 5 }) => {
  return (
    <div className="skeleton-list">
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className="skeleton-list-item">
          <div className="skeleton-avatar"></div>
          <div className="skeleton-list-content">
            <div className="skeleton-title"></div>
            <div className="skeleton-text"></div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Dots loading animation
export const DotsLoader = ({ color = '#e74c3c', size = 'medium' }) => {
  return (
    <div className={`dots-loader ${size}`}>
      <div className="dot" style={{ backgroundColor: color }}></div>
      <div className="dot" style={{ backgroundColor: color }}></div>
      <div className="dot" style={{ backgroundColor: color }}></div>
    </div>
  );
};

// Pulse loading animation
export const PulseLoader = ({ color = '#e74c3c', size = 'medium', count = 3 }) => {
  return (
    <div className={`pulse-loader ${size}`}>
      {Array.from({ length: count }, (_, index) => (
        <div 
          key={index}
          className="pulse" 
          style={{ 
            backgroundColor: color,
            animationDelay: `${index * 0.15}s`
          }}
        ></div>
      ))}
    </div>
  );
};

// Loading overlay for buttons
export const ButtonLoader = ({ size = 'small', color = '#ffffff' }) => {
  return (
    <div className={`button-loader ${size}`}>
      <div 
        className="button-spinner" 
        style={{ borderTopColor: color }}
      ></div>
    </div>
  );
};

export default LoadingSpinner;
