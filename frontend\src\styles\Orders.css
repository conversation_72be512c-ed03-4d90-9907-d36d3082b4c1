/* Orders Page Styles */
.orders-page {
  padding: 40px 0;
  min-height: calc(100vh - 160px);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
}

.page-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Orders List */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
  max-width: 1000px;
  margin: 0 auto;
}

.order-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Order Header */
.order-header {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.3rem;
}

.order-date {
  opacity: 0.9;
  font-size: 0.9rem;
}

.order-status {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.order-status.pending {
  background: #fff3cd;
  color: #856404;
}

.order-status.confirmed {
  background: #d1ecf1;
  color: #0c5460;
}

.order-status.preparing {
  background: #d4edda;
  color: #155724;
}

.order-status.ready {
  background: #cce5ff;
  color: #004085;
}

.order-status.delivered {
  background: #d4edda;
  color: #155724;
}

.order-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

/* Order Content */
.order-content {
  padding: 25px;
}

.order-details {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-bottom: 20px;
}

/* Order Items */
.order-items h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.item-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.item-image {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  object-fit: cover;
}

.item-details {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.item-price {
  color: #e74c3c;
  font-weight: 500;
}

.item-quantity {
  color: #666;
  font-size: 0.9rem;
}

/* Order Summary */
.order-summary h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #555;
}

.summary-item.total {
  border-top: 2px solid #e9ecef;
  padding-top: 10px;
  margin-top: 10px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

/* Order Meta */
.order-meta {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.meta-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.meta-value {
  color: #555;
  font-size: 0.95rem;
}

/* Order Actions */
.order-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.action-btn.primary {
  background: #e74c3c;
  color: white;
}

.action-btn.primary:hover {
  background: #c0392b;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.action-btn.danger {
  background: #dc3545;
  color: white;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Bill Split Info */
.bill-split-info {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.split-badge {
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 10px;
}

.split-details {
  margin-top: 10px;
  font-size: 0.9rem;
  color: #155724;
}

/* Empty State */
.empty-orders {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-orders h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.empty-orders p {
  margin-bottom: 25px;
  font-size: 1.1rem;
}

.empty-orders .btn {
  background: #e74c3c;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: background 0.3s ease;
}

.empty-orders .btn:hover {
  background: #c0392b;
}

/* Loading State */
.orders-loading {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .orders-page {
    padding: 20px 0;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .order-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .order-details {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .order-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .meta-grid {
    grid-template-columns: 1fr;
  }

  .order-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .order-card {
    margin: 0 -10px;
    border-radius: 0;
  }

  .order-content {
    padding: 20px;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .action-btn {
    width: 100%;
    padding: 10px;
  }
}
