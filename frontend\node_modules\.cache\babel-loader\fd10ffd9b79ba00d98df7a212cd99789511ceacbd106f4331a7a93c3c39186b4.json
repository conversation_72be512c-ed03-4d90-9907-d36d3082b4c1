{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\pages\\\\LoyaltyDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/LoyaltyDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoyaltyDashboard = () => {\n  _s();\n  const [loyaltyData, setLoyaltyData] = useState(null);\n  const [transactions, setTransactions] = useState([]);\n  const [leaderboard, setLeaderboard] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [redeemAmount, setRedeemAmount] = useState(100);\n  const [referralCode, setReferralCode] = useState('');\n  const {\n    user\n  } = useAuth();\n  useEffect(() => {\n    fetchLoyaltyData();\n    fetchTransactions();\n    fetchLeaderboard();\n  }, []);\n  const fetchLoyaltyData = async () => {\n    try {\n      const response = await axios.get('/api/loyalty/profile');\n      setLoyaltyData(response.data);\n    } catch (error) {\n      toast.error('Failed to load loyalty data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchTransactions = async () => {\n    try {\n      const response = await axios.get('/api/loyalty/transactions');\n      setTransactions(response.data.transactions);\n    } catch (error) {\n      console.error('Failed to load transactions');\n    }\n  };\n  const fetchLeaderboard = async () => {\n    try {\n      const response = await axios.get('/api/loyalty/leaderboard');\n      setLeaderboard(response.data);\n    } catch (error) {\n      console.error('Failed to load leaderboard');\n    }\n  };\n  const handleRedeem = async () => {\n    try {\n      const response = await axios.post('/api/loyalty/redeem', {\n        points: redeemAmount,\n        rewardType: 'discount'\n      });\n      toast.success(`Redeemed ${redeemAmount} points for $${response.data.discountValue} discount!`);\n      toast.info(`Discount code: ${response.data.discountCode}`);\n      fetchLoyaltyData();\n      fetchTransactions();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to redeem points');\n    }\n  };\n  const handleApplyReferral = async () => {\n    try {\n      await axios.post('/api/loyalty/referral', {\n        referralCode\n      });\n      toast.success('Referral code applied successfully!');\n      setReferralCode('');\n      fetchLoyaltyData();\n      fetchTransactions();\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to apply referral code');\n    }\n  };\n  const copyReferralCode = () => {\n    var _loyaltyData$loyaltyP;\n    if (loyaltyData !== null && loyaltyData !== void 0 && (_loyaltyData$loyaltyP = loyaltyData.loyaltyProgram) !== null && _loyaltyData$loyaltyP !== void 0 && _loyaltyData$loyaltyP.referralCode) {\n      navigator.clipboard.writeText(loyaltyData.loyaltyProgram.referralCode);\n      toast.success('Referral code copied to clipboard!');\n    }\n  };\n  const getTierColor = tier => {\n    const colors = {\n      bronze: '#CD7F32',\n      silver: '#C0C0C0',\n      gold: '#FFD700',\n      platinum: '#E5E4E2'\n    };\n    return colors[tier] || colors.bronze;\n  };\n  const getTierIcon = tier => {\n    const icons = {\n      bronze: '🥉',\n      silver: '🥈',\n      gold: '🥇',\n      platinum: '💎'\n    };\n    return icons[tier] || icons.bronze;\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading loyalty dashboard...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 12\n    }, this);\n  }\n  if (!loyaltyData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: \"Failed to load loyalty data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  }\n  const {\n    loyaltyProgram,\n    tierBenefits,\n    pointsExpiringSoon\n  } = loyaltyData;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loyalty-dashboard\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loyalty-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Loyalty Rewards\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Earn points, unlock rewards, and enjoy exclusive benefits!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loyalty-overview\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tier-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tier-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tier-icon\",\n              children: getTierIcon(loyaltyProgram.tier)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tier-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  color: getTierColor(loyaltyProgram.tier)\n                },\n                children: [loyaltyProgram.tier.toUpperCase(), \" MEMBER\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"points-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"points-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"points-value\",\n                children: loyaltyProgram.availablePoints\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"points-label\",\n                children: \"Available Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"points-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"points-value\",\n                children: loyaltyProgram.lifetimePoints\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"points-label\",\n                children: \"Lifetime Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), loyaltyProgram.tierProgress.nextTier && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tier-progress\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Progress to \", loyaltyProgram.tierProgress.nextTier.toUpperCase()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${loyaltyProgram.tierProgress.currentSpend / loyaltyProgram.tierProgress.nextTierSpend * 100}%`,\n                backgroundColor: getTierColor(loyaltyProgram.tierProgress.nextTier)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"$\", loyaltyProgram.tierProgress.currentSpend, \" / $\", loyaltyProgram.tierProgress.nextTierSpend]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tier-benefits\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Your Benefits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefits-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [tierBenefits.pointsMultiplier, \"x Points\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83D\\uDCB0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [tierBenefits.discountPercentage, \"% Discount\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83D\\uDE9A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tierBenefits.freeDelivery ? 'Free Delivery' : 'Paid Delivery'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83C\\uDF82\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [tierBenefits.birthdayBonus, \" Birthday Points\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), pointsExpiringSoon > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-warning\",\n        children: [\"\\u26A0\\uFE0F You have \", pointsExpiringSoon, \" points expiring in the next 30 days!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loyalty-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'overview' ? 'active' : ''}`,\n          onClick: () => setActiveTab('overview'),\n          children: \"Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'redeem' ? 'active' : ''}`,\n          onClick: () => setActiveTab('redeem'),\n          children: \"Redeem Points\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'referral' ? 'active' : ''}`,\n          onClick: () => setActiveTab('referral'),\n          children: \"Referrals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'history' ? 'active' : ''}`,\n          onClick: () => setActiveTab('history'),\n          children: \"History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${activeTab === 'leaderboard' ? 'active' : ''}`,\n          onClick: () => setActiveTab('leaderboard'),\n          children: \"Leaderboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overview-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"achievements-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Recent Achievements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), loyaltyProgram.achievements.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"achievements-grid\",\n              children: loyaltyProgram.achievements.slice(-3).map((achievement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"achievement-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: achievement.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: achievement.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"achievement-points\",\n                  children: [\"+\", achievement.pointsAwarded, \" points\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"achievement-date\",\n                  children: formatDate(achievement.unlockedAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No achievements yet. Start ordering to unlock achievements!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), activeTab === 'redeem' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"redeem-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Redeem Your Points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"redeem-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"redeem-calculator\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Points to Redeem (Min: 100):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"100\",\n                step: \"50\",\n                value: redeemAmount,\n                onChange: e => setRedeemAmount(parseInt(e.target.value))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Discount Value: $\", (redeemAmount * 0.01).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary\",\n                onClick: handleRedeem,\n                disabled: redeemAmount < 100 || redeemAmount > loyaltyProgram.availablePoints,\n                children: \"Redeem Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"redeem-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"How it works:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"100 points = $1.00 discount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Minimum redemption: 100 points\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Discount codes are valid for 30 days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Use discount codes at checkout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), activeTab === 'referral' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"referral-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"referral-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-referral\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Your Referral Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"referral-code-display\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"referral-code\",\n                  children: loyaltyProgram.referralCode\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"copy-btn\",\n                  onClick: copyReferralCode,\n                  children: \"Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Share this code with friends and both of you get 100 points!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), loyaltyProgram.referrals.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"referral-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: [\"Your Referrals (\", loyaltyProgram.referrals.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"referral-list\",\n                  children: loyaltyProgram.referrals.map((referral, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"referral-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: referral.user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"+\", referral.pointsAwarded, \" points\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: formatDate(referral.createdAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), !loyaltyProgram.referredBy && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"apply-referral\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Have a Referral Code?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"referral-input\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Enter referral code\",\n                  value: referralCode,\n                  onChange: e => setReferralCode(e.target.value.toUpperCase())\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  onClick: handleApplyReferral,\n                  disabled: !referralCode,\n                  children: \"Apply Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Points History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"transactions-list\",\n            children: transactions.map((transaction, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `transaction-item ${transaction.type}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"transaction-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"transaction-description\",\n                  children: transaction.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"transaction-date\",\n                  children: formatDate(transaction.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `transaction-points ${transaction.type}`,\n                children: [transaction.type === 'redeemed' ? '-' : '+', Math.abs(transaction.points), \" pts\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this), activeTab === 'leaderboard' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"leaderboard-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Top Members\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"leaderboard-list\",\n            children: leaderboard.map((member, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"leaderboard-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"rank\",\n                children: [\"#\", index + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"member-name\",\n                children: member.customer.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"member-tier\",\n                children: getTierIcon(member.tier)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"member-points\",\n                children: [member.lifetimePoints, \" pts\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(LoyaltyDashboard, \"3aVVvO3GZvzQ48+nd2MXD1cdhkQ=\", false, function () {\n  return [useAuth];\n});\n_c = LoyaltyDashboard;\nexport default LoyaltyDashboard;\nvar _c;\n$RefreshReg$(_c, \"LoyaltyDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "toast", "useAuth", "jsxDEV", "_jsxDEV", "LoyaltyDashboard", "_s", "loyaltyData", "setLoyaltyData", "transactions", "setTransactions", "leaderboard", "setLeaderboard", "loading", "setLoading", "activeTab", "setActiveTab", "redeemAmount", "setRedeemAmount", "referralCode", "setReferralCode", "user", "fetchLoyaltyData", "fetchTransactions", "fetchLeaderboard", "response", "get", "data", "error", "console", "handleRedeem", "post", "points", "rewardType", "success", "discountValue", "info", "discountCode", "_error$response", "_error$response$data", "message", "handleApplyReferral", "_error$response2", "_error$response2$data", "copyReferralCode", "_loyaltyData$loyaltyP", "loyaltyProgram", "navigator", "clipboard", "writeText", "getTierColor", "tier", "colors", "bronze", "silver", "gold", "platinum", "getTierIcon", "icons", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tierBenefits", "pointsExpiringSoon", "style", "color", "toUpperCase", "name", "availablePoints", "lifetimePoints", "tierProgress", "nextTier", "width", "currentSpend", "nextTierSpend", "backgroundColor", "pointsMultiplier", "discountPercentage", "freeDelivery", "birthdayBonus", "onClick", "achievements", "length", "slice", "map", "achievement", "index", "title", "description", "pointsAwarded", "unlockedAt", "type", "min", "step", "value", "onChange", "e", "parseInt", "target", "toFixed", "disabled", "referrals", "referral", "createdAt", "<PERSON><PERSON><PERSON>", "placeholder", "transaction", "Math", "abs", "member", "customer", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/pages/LoyaltyDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/LoyaltyDashboard.css';\n\nconst LoyaltyDashboard = () => {\n  const [loyaltyData, setLoyaltyData] = useState(null);\n  const [transactions, setTransactions] = useState([]);\n  const [leaderboard, setLeaderboard] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [redeemAmount, setRedeemAmount] = useState(100);\n  const [referralCode, setReferralCode] = useState('');\n\n  const { user } = useAuth();\n\n  useEffect(() => {\n    fetchLoyaltyData();\n    fetchTransactions();\n    fetchLeaderboard();\n  }, []);\n\n  const fetchLoyaltyData = async () => {\n    try {\n      const response = await axios.get('/api/loyalty/profile');\n      setLoyaltyData(response.data);\n    } catch (error) {\n      toast.error('Failed to load loyalty data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchTransactions = async () => {\n    try {\n      const response = await axios.get('/api/loyalty/transactions');\n      setTransactions(response.data.transactions);\n    } catch (error) {\n      console.error('Failed to load transactions');\n    }\n  };\n\n  const fetchLeaderboard = async () => {\n    try {\n      const response = await axios.get('/api/loyalty/leaderboard');\n      setLeaderboard(response.data);\n    } catch (error) {\n      console.error('Failed to load leaderboard');\n    }\n  };\n\n  const handleRedeem = async () => {\n    try {\n      const response = await axios.post('/api/loyalty/redeem', {\n        points: redeemAmount,\n        rewardType: 'discount'\n      });\n      \n      toast.success(`Redeemed ${redeemAmount} points for $${response.data.discountValue} discount!`);\n      toast.info(`Discount code: ${response.data.discountCode}`);\n      \n      fetchLoyaltyData();\n      fetchTransactions();\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to redeem points');\n    }\n  };\n\n  const handleApplyReferral = async () => {\n    try {\n      await axios.post('/api/loyalty/referral', { referralCode });\n      toast.success('Referral code applied successfully!');\n      setReferralCode('');\n      fetchLoyaltyData();\n      fetchTransactions();\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to apply referral code');\n    }\n  };\n\n  const copyReferralCode = () => {\n    if (loyaltyData?.loyaltyProgram?.referralCode) {\n      navigator.clipboard.writeText(loyaltyData.loyaltyProgram.referralCode);\n      toast.success('Referral code copied to clipboard!');\n    }\n  };\n\n  const getTierColor = (tier) => {\n    const colors = {\n      bronze: '#CD7F32',\n      silver: '#C0C0C0',\n      gold: '#FFD700',\n      platinum: '#E5E4E2'\n    };\n    return colors[tier] || colors.bronze;\n  };\n\n  const getTierIcon = (tier) => {\n    const icons = {\n      bronze: '🥉',\n      silver: '🥈',\n      gold: '🥇',\n      platinum: '💎'\n    };\n    return icons[tier] || icons.bronze;\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading loyalty dashboard...</div>;\n  }\n\n  if (!loyaltyData) {\n    return <div className=\"error\">Failed to load loyalty data</div>;\n  }\n\n  const { loyaltyProgram, tierBenefits, pointsExpiringSoon } = loyaltyData;\n\n  return (\n    <div className=\"loyalty-dashboard\">\n      <div className=\"container\">\n        <div className=\"loyalty-header\">\n          <h1>Loyalty Rewards</h1>\n          <p>Earn points, unlock rewards, and enjoy exclusive benefits!</p>\n        </div>\n\n        {/* Loyalty Overview Card */}\n        <div className=\"loyalty-overview\">\n          <div className=\"tier-card\">\n            <div className=\"tier-info\">\n              <span className=\"tier-icon\">{getTierIcon(loyaltyProgram.tier)}</span>\n              <div className=\"tier-details\">\n                <h2 style={{ color: getTierColor(loyaltyProgram.tier) }}>\n                  {loyaltyProgram.tier.toUpperCase()} MEMBER\n                </h2>\n                <p>{user.name}</p>\n              </div>\n            </div>\n            <div className=\"points-summary\">\n              <div className=\"points-item\">\n                <span className=\"points-value\">{loyaltyProgram.availablePoints}</span>\n                <span className=\"points-label\">Available Points</span>\n              </div>\n              <div className=\"points-item\">\n                <span className=\"points-value\">{loyaltyProgram.lifetimePoints}</span>\n                <span className=\"points-label\">Lifetime Points</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Tier Progress */}\n          {loyaltyProgram.tierProgress.nextTier && (\n            <div className=\"tier-progress\">\n              <h3>Progress to {loyaltyProgram.tierProgress.nextTier.toUpperCase()}</h3>\n              <div className=\"progress-bar\">\n                <div \n                  className=\"progress-fill\"\n                  style={{ \n                    width: `${(loyaltyProgram.tierProgress.currentSpend / loyaltyProgram.tierProgress.nextTierSpend) * 100}%`,\n                    backgroundColor: getTierColor(loyaltyProgram.tierProgress.nextTier)\n                  }}\n                ></div>\n              </div>\n              <p>\n                ${loyaltyProgram.tierProgress.currentSpend} / ${loyaltyProgram.tierProgress.nextTierSpend}\n              </p>\n            </div>\n          )}\n\n          {/* Tier Benefits */}\n          <div className=\"tier-benefits\">\n            <h3>Your Benefits</h3>\n            <div className=\"benefits-grid\">\n              <div className=\"benefit-item\">\n                <span className=\"benefit-icon\">⭐</span>\n                <span>{tierBenefits.pointsMultiplier}x Points</span>\n              </div>\n              <div className=\"benefit-item\">\n                <span className=\"benefit-icon\">💰</span>\n                <span>{tierBenefits.discountPercentage}% Discount</span>\n              </div>\n              <div className=\"benefit-item\">\n                <span className=\"benefit-icon\">🚚</span>\n                <span>{tierBenefits.freeDelivery ? 'Free Delivery' : 'Paid Delivery'}</span>\n              </div>\n              <div className=\"benefit-item\">\n                <span className=\"benefit-icon\">🎂</span>\n                <span>{tierBenefits.birthdayBonus} Birthday Points</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Alerts */}\n        {pointsExpiringSoon > 0 && (\n          <div className=\"alert alert-warning\">\n            ⚠️ You have {pointsExpiringSoon} points expiring in the next 30 days!\n          </div>\n        )}\n\n        {/* Tabs */}\n        <div className=\"loyalty-tabs\">\n          <button \n            className={`tab ${activeTab === 'overview' ? 'active' : ''}`}\n            onClick={() => setActiveTab('overview')}\n          >\n            Overview\n          </button>\n          <button \n            className={`tab ${activeTab === 'redeem' ? 'active' : ''}`}\n            onClick={() => setActiveTab('redeem')}\n          >\n            Redeem Points\n          </button>\n          <button \n            className={`tab ${activeTab === 'referral' ? 'active' : ''}`}\n            onClick={() => setActiveTab('referral')}\n          >\n            Referrals\n          </button>\n          <button \n            className={`tab ${activeTab === 'history' ? 'active' : ''}`}\n            onClick={() => setActiveTab('history')}\n          >\n            History\n          </button>\n          <button \n            className={`tab ${activeTab === 'leaderboard' ? 'active' : ''}`}\n            onClick={() => setActiveTab('leaderboard')}\n          >\n            Leaderboard\n          </button>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"tab-content\">\n          {activeTab === 'overview' && (\n            <div className=\"overview-content\">\n              <div className=\"achievements-section\">\n                <h3>Recent Achievements</h3>\n                {loyaltyProgram.achievements.length > 0 ? (\n                  <div className=\"achievements-grid\">\n                    {loyaltyProgram.achievements.slice(-3).map((achievement, index) => (\n                      <div key={index} className=\"achievement-card\">\n                        <h4>{achievement.title}</h4>\n                        <p>{achievement.description}</p>\n                        <span className=\"achievement-points\">+{achievement.pointsAwarded} points</span>\n                        <span className=\"achievement-date\">{formatDate(achievement.unlockedAt)}</span>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <p>No achievements yet. Start ordering to unlock achievements!</p>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'redeem' && (\n            <div className=\"redeem-content\">\n              <h3>Redeem Your Points</h3>\n              <div className=\"redeem-section\">\n                <div className=\"redeem-calculator\">\n                  <label>Points to Redeem (Min: 100):</label>\n                  <input\n                    type=\"number\"\n                    min=\"100\"\n                    step=\"50\"\n                    value={redeemAmount}\n                    onChange={(e) => setRedeemAmount(parseInt(e.target.value))}\n                  />\n                  <p>Discount Value: ${(redeemAmount * 0.01).toFixed(2)}</p>\n                  <button \n                    className=\"btn btn-primary\"\n                    onClick={handleRedeem}\n                    disabled={redeemAmount < 100 || redeemAmount > loyaltyProgram.availablePoints}\n                  >\n                    Redeem Points\n                  </button>\n                </div>\n                \n                <div className=\"redeem-info\">\n                  <h4>How it works:</h4>\n                  <ul>\n                    <li>100 points = $1.00 discount</li>\n                    <li>Minimum redemption: 100 points</li>\n                    <li>Discount codes are valid for 30 days</li>\n                    <li>Use discount codes at checkout</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'referral' && (\n            <div className=\"referral-content\">\n              <div className=\"referral-section\">\n                <div className=\"my-referral\">\n                  <h3>Your Referral Code</h3>\n                  <div className=\"referral-code-display\">\n                    <span className=\"referral-code\">{loyaltyProgram.referralCode}</span>\n                    <button className=\"copy-btn\" onClick={copyReferralCode}>\n                      Copy\n                    </button>\n                  </div>\n                  <p>Share this code with friends and both of you get 100 points!</p>\n                  \n                  {loyaltyProgram.referrals.length > 0 && (\n                    <div className=\"referral-stats\">\n                      <h4>Your Referrals ({loyaltyProgram.referrals.length})</h4>\n                      <div className=\"referral-list\">\n                        {loyaltyProgram.referrals.map((referral, index) => (\n                          <div key={index} className=\"referral-item\">\n                            <span>{referral.user.name}</span>\n                            <span>+{referral.pointsAwarded} points</span>\n                            <span>{formatDate(referral.createdAt)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {!loyaltyProgram.referredBy && (\n                  <div className=\"apply-referral\">\n                    <h3>Have a Referral Code?</h3>\n                    <div className=\"referral-input\">\n                      <input\n                        type=\"text\"\n                        placeholder=\"Enter referral code\"\n                        value={referralCode}\n                        onChange={(e) => setReferralCode(e.target.value.toUpperCase())}\n                      />\n                      <button \n                        className=\"btn btn-primary\"\n                        onClick={handleApplyReferral}\n                        disabled={!referralCode}\n                      >\n                        Apply Code\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'history' && (\n            <div className=\"history-content\">\n              <h3>Points History</h3>\n              <div className=\"transactions-list\">\n                {transactions.map((transaction, index) => (\n                  <div key={index} className={`transaction-item ${transaction.type}`}>\n                    <div className=\"transaction-info\">\n                      <span className=\"transaction-description\">{transaction.description}</span>\n                      <span className=\"transaction-date\">{formatDate(transaction.createdAt)}</span>\n                    </div>\n                    <span className={`transaction-points ${transaction.type}`}>\n                      {transaction.type === 'redeemed' ? '-' : '+'}{Math.abs(transaction.points)} pts\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'leaderboard' && (\n            <div className=\"leaderboard-content\">\n              <h3>Top Members</h3>\n              <div className=\"leaderboard-list\">\n                {leaderboard.map((member, index) => (\n                  <div key={index} className=\"leaderboard-item\">\n                    <span className=\"rank\">#{index + 1}</span>\n                    <span className=\"member-name\">{member.customer.name}</span>\n                    <span className=\"member-tier\">{getTierIcon(member.tier)}</span>\n                    <span className=\"member-points\">{member.lifetimePoints} pts</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoyaltyDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,GAAG,CAAC;EACrD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM;IAAEuB;EAAK,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAE1BH,SAAS,CAAC,MAAM;IACduB,gBAAgB,CAAC,CAAC;IAClBC,iBAAiB,CAAC,CAAC;IACnBC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,sBAAsB,CAAC;MACxDlB,cAAc,CAACiB,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3B,KAAK,CAAC2B,KAAK,CAAC,6BAA6B,CAAC;IAC5C,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,2BAA2B,CAAC;MAC7DhB,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAClB,YAAY,CAAC;IAC7C,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,0BAA0B,CAAC;MAC5Dd,cAAc,CAACa,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAC;IAC7C;EACF,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMzB,KAAK,CAAC+B,IAAI,CAAC,qBAAqB,EAAE;QACvDC,MAAM,EAAEf,YAAY;QACpBgB,UAAU,EAAE;MACd,CAAC,CAAC;MAEFhC,KAAK,CAACiC,OAAO,CAAC,YAAYjB,YAAY,gBAAgBQ,QAAQ,CAACE,IAAI,CAACQ,aAAa,YAAY,CAAC;MAC9FlC,KAAK,CAACmC,IAAI,CAAC,kBAAkBX,QAAQ,CAACE,IAAI,CAACU,YAAY,EAAE,CAAC;MAE1Df,gBAAgB,CAAC,CAAC;MAClBC,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACdtC,KAAK,CAAC2B,KAAK,CAAC,EAAAU,eAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,yBAAyB,CAAC;IACzE;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMzC,KAAK,CAAC+B,IAAI,CAAC,uBAAuB,EAAE;QAAEZ;MAAa,CAAC,CAAC;MAC3DlB,KAAK,CAACiC,OAAO,CAAC,qCAAqC,CAAC;MACpDd,eAAe,CAAC,EAAE,CAAC;MACnBE,gBAAgB,CAAC,CAAC;MAClBC,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACd1C,KAAK,CAAC2B,KAAK,CAAC,EAAAc,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAAI,+BAA+B,CAAC;IAC/E;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC7B,IAAItC,WAAW,aAAXA,WAAW,gBAAAsC,qBAAA,GAAXtC,WAAW,CAAEuC,cAAc,cAAAD,qBAAA,eAA3BA,qBAAA,CAA6B1B,YAAY,EAAE;MAC7C4B,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1C,WAAW,CAACuC,cAAc,CAAC3B,YAAY,CAAC;MACtElB,KAAK,CAACiC,OAAO,CAAC,oCAAoC,CAAC;IACrD;EACF,CAAC;EAED,MAAMgB,YAAY,GAAIC,IAAI,IAAK;IAC7B,MAAMC,MAAM,GAAG;MACbC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOJ,MAAM,CAACD,IAAI,CAAC,IAAIC,MAAM,CAACC,MAAM;EACtC,CAAC;EAED,MAAMI,WAAW,GAAIN,IAAI,IAAK;IAC5B,MAAMO,KAAK,GAAG;MACZL,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOE,KAAK,CAACP,IAAI,CAAC,IAAIO,KAAK,CAACL,MAAM;EACpC,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAIpD,OAAO,EAAE;IACX,oBAAOT,OAAA;MAAK8D,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACpE;EAEA,IAAI,CAAChE,WAAW,EAAE;IAChB,oBAAOH,OAAA;MAAK8D,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjE;EAEA,MAAM;IAAEzB,cAAc;IAAE0B,YAAY;IAAEC;EAAmB,CAAC,GAAGlE,WAAW;EAExE,oBACEH,OAAA;IAAK8D,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC/D,OAAA;MAAK8D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB/D,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/D,OAAA;UAAA+D,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBnE,OAAA;UAAA+D,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAGNnE,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/D,OAAA;UAAK8D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/D,OAAA;YAAK8D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/D,OAAA;cAAM8D,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEV,WAAW,CAACX,cAAc,CAACK,IAAI;YAAC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrEnE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/D,OAAA;gBAAIsE,KAAK,EAAE;kBAAEC,KAAK,EAAEzB,YAAY,CAACJ,cAAc,CAACK,IAAI;gBAAE,CAAE;gBAAAgB,QAAA,GACrDrB,cAAc,CAACK,IAAI,CAACyB,WAAW,CAAC,CAAC,EAAC,SACrC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnE,OAAA;gBAAA+D,QAAA,EAAI9C,IAAI,CAACwD;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnE,OAAA;YAAK8D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/D,OAAA;cAAK8D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B/D,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAErB,cAAc,CAACgC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEnE,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B/D,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAErB,cAAc,CAACiC;cAAc;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrEnE,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLzB,cAAc,CAACkC,YAAY,CAACC,QAAQ,iBACnC7E,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/D,OAAA;YAAA+D,QAAA,GAAI,cAAY,EAACrB,cAAc,CAACkC,YAAY,CAACC,QAAQ,CAACL,WAAW,CAAC,CAAC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEnE,OAAA;YAAK8D,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B/D,OAAA;cACE8D,SAAS,EAAC,eAAe;cACzBQ,KAAK,EAAE;gBACLQ,KAAK,EAAE,GAAIpC,cAAc,CAACkC,YAAY,CAACG,YAAY,GAAGrC,cAAc,CAACkC,YAAY,CAACI,aAAa,GAAI,GAAG,GAAG;gBACzGC,eAAe,EAAEnC,YAAY,CAACJ,cAAc,CAACkC,YAAY,CAACC,QAAQ;cACpE;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnE,OAAA;YAAA+D,QAAA,GAAG,GACA,EAACrB,cAAc,CAACkC,YAAY,CAACG,YAAY,EAAC,MAAI,EAACrC,cAAc,CAACkC,YAAY,CAACI,aAAa;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAGDnE,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/D,OAAA;YAAA+D,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBnE,OAAA;YAAK8D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B/D,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/D,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCnE,OAAA;gBAAA+D,QAAA,GAAOK,YAAY,CAACc,gBAAgB,EAAC,UAAQ;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/D,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCnE,OAAA;gBAAA+D,QAAA,GAAOK,YAAY,CAACe,kBAAkB,EAAC,YAAU;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/D,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCnE,OAAA;gBAAA+D,QAAA,EAAOK,YAAY,CAACgB,YAAY,GAAG,eAAe,GAAG;cAAe;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/D,OAAA;gBAAM8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCnE,OAAA;gBAAA+D,QAAA,GAAOK,YAAY,CAACiB,aAAa,EAAC,kBAAgB;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLE,kBAAkB,GAAG,CAAC,iBACrBrE,OAAA;QAAK8D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,GAAC,wBACvB,EAACM,kBAAkB,EAAC,uCAClC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAGDnE,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/D,OAAA;UACE8D,SAAS,EAAE,OAAOnD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7D2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,UAAU,CAAE;UAAAmD,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnE,OAAA;UACE8D,SAAS,EAAE,OAAOnD,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3D2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,QAAQ,CAAE;UAAAmD,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnE,OAAA;UACE8D,SAAS,EAAE,OAAOnD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7D2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,UAAU,CAAE;UAAAmD,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnE,OAAA;UACE8D,SAAS,EAAE,OAAOnD,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC5D2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,SAAS,CAAE;UAAAmD,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnE,OAAA;UACE8D,SAAS,EAAE,OAAOnD,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChE2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,aAAa,CAAE;UAAAmD,QAAA,EAC5C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNnE,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAC,QAAA,GACzBpD,SAAS,KAAK,UAAU,iBACvBX,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/D,OAAA;YAAK8D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/D,OAAA;cAAA+D,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC3BzB,cAAc,CAAC6C,YAAY,CAACC,MAAM,GAAG,CAAC,gBACrCxF,OAAA;cAAK8D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC/BrB,cAAc,CAAC6C,YAAY,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAC5D5F,OAAA;gBAAiB8D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC3C/D,OAAA;kBAAA+D,QAAA,EAAK4B,WAAW,CAACE;gBAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5BnE,OAAA;kBAAA+D,QAAA,EAAI4B,WAAW,CAACG;gBAAW;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChCnE,OAAA;kBAAM8D,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,GAAC,EAAC4B,WAAW,CAACI,aAAa,EAAC,SAAO;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/EnE,OAAA;kBAAM8D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAER,UAAU,CAACoC,WAAW,CAACK,UAAU;gBAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAJtEyB,KAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENnE,OAAA;cAAA+D,QAAA,EAAG;YAA2D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxD,SAAS,KAAK,QAAQ,iBACrBX,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/D,OAAA;YAAA+D,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnE,OAAA;YAAK8D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/D,OAAA;cAAK8D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/D,OAAA;gBAAA+D,QAAA,EAAO;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CnE,OAAA;gBACEiG,IAAI,EAAC,QAAQ;gBACbC,GAAG,EAAC,KAAK;gBACTC,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEvF,YAAa;gBACpBwF,QAAQ,EAAGC,CAAC,IAAKxF,eAAe,CAACyF,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACFnE,OAAA;gBAAA+D,QAAA,GAAG,mBAAiB,EAAC,CAAClD,YAAY,GAAG,IAAI,EAAE4F,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DnE,OAAA;gBACE8D,SAAS,EAAC,iBAAiB;gBAC3BwB,OAAO,EAAE5D,YAAa;gBACtBgF,QAAQ,EAAE7F,YAAY,GAAG,GAAG,IAAIA,YAAY,GAAG6B,cAAc,CAACgC,eAAgB;gBAAAX,QAAA,EAC/E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnE,OAAA;cAAK8D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B/D,OAAA;gBAAA+D,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBnE,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA;kBAAA+D,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpCnE,OAAA;kBAAA+D,QAAA,EAAI;gBAA8B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvCnE,OAAA;kBAAA+D,QAAA,EAAI;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CnE,OAAA;kBAAA+D,QAAA,EAAI;gBAA8B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxD,SAAS,KAAK,UAAU,iBACvBX,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/D,OAAA;YAAK8D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B/D,OAAA;cAAK8D,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B/D,OAAA;gBAAA+D,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BnE,OAAA;gBAAK8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC/D,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAErB,cAAc,CAAC3B;gBAAY;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpEnE,OAAA;kBAAQ8D,SAAS,EAAC,UAAU;kBAACwB,OAAO,EAAE9C,gBAAiB;kBAAAuB,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNnE,OAAA;gBAAA+D,QAAA,EAAG;cAA4D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAElEzB,cAAc,CAACiE,SAAS,CAACnB,MAAM,GAAG,CAAC,iBAClCxF,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/D,OAAA;kBAAA+D,QAAA,GAAI,kBAAgB,EAACrB,cAAc,CAACiE,SAAS,CAACnB,MAAM,EAAC,GAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3DnE,OAAA;kBAAK8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC3BrB,cAAc,CAACiE,SAAS,CAACjB,GAAG,CAAC,CAACkB,QAAQ,EAAEhB,KAAK,kBAC5C5F,OAAA;oBAAiB8D,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBACxC/D,OAAA;sBAAA+D,QAAA,EAAO6C,QAAQ,CAAC3F,IAAI,CAACwD;oBAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjCnE,OAAA;sBAAA+D,QAAA,GAAM,GAAC,EAAC6C,QAAQ,CAACb,aAAa,EAAC,SAAO;oBAAA;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7CnE,OAAA;sBAAA+D,QAAA,EAAOR,UAAU,CAACqD,QAAQ,CAACC,SAAS;oBAAC;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAHrCyB,KAAK;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL,CAACzB,cAAc,CAACoE,UAAU,iBACzB9G,OAAA;cAAK8D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B/D,OAAA;gBAAA+D,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BnE,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/D,OAAA;kBACEiG,IAAI,EAAC,MAAM;kBACXc,WAAW,EAAC,qBAAqB;kBACjCX,KAAK,EAAErF,YAAa;kBACpBsF,QAAQ,EAAGC,CAAC,IAAKtF,eAAe,CAACsF,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC5B,WAAW,CAAC,CAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACFnE,OAAA;kBACE8D,SAAS,EAAC,iBAAiB;kBAC3BwB,OAAO,EAAEjD,mBAAoB;kBAC7BqE,QAAQ,EAAE,CAAC3F,YAAa;kBAAAgD,QAAA,EACzB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxD,SAAS,KAAK,SAAS,iBACtBX,OAAA;UAAK8D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/D,OAAA;YAAA+D,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBnE,OAAA;YAAK8D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/B1D,YAAY,CAACqF,GAAG,CAAC,CAACsB,WAAW,EAAEpB,KAAK,kBACnC5F,OAAA;cAAiB8D,SAAS,EAAE,oBAAoBkD,WAAW,CAACf,IAAI,EAAG;cAAAlC,QAAA,gBACjE/D,OAAA;gBAAK8D,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B/D,OAAA;kBAAM8D,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAEiD,WAAW,CAAClB;gBAAW;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1EnE,OAAA;kBAAM8D,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAER,UAAU,CAACyD,WAAW,CAACH,SAAS;gBAAC;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACNnE,OAAA;gBAAM8D,SAAS,EAAE,sBAAsBkD,WAAW,CAACf,IAAI,EAAG;gBAAAlC,QAAA,GACvDiD,WAAW,CAACf,IAAI,KAAK,UAAU,GAAG,GAAG,GAAG,GAAG,EAAEgB,IAAI,CAACC,GAAG,CAACF,WAAW,CAACpF,MAAM,CAAC,EAAC,MAC7E;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAPCyB,KAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxD,SAAS,KAAK,aAAa,iBAC1BX,OAAA;UAAK8D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC/D,OAAA;YAAA+D,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBnE,OAAA;YAAK8D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BxD,WAAW,CAACmF,GAAG,CAAC,CAACyB,MAAM,EAAEvB,KAAK,kBAC7B5F,OAAA;cAAiB8D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC3C/D,OAAA;gBAAM8D,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,GAAC,EAAC6B,KAAK,GAAG,CAAC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CnE,OAAA;gBAAM8D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEoD,MAAM,CAACC,QAAQ,CAAC3C;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DnE,OAAA;gBAAM8D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEV,WAAW,CAAC8D,MAAM,CAACpE,IAAI;cAAC;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/DnE,OAAA;gBAAM8D,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAEoD,MAAM,CAACxC,cAAc,EAAC,MAAI;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAJ1DyB,KAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CAnYID,gBAAgB;EAAA,QASHH,OAAO;AAAA;AAAAuH,EAAA,GATpBpH,gBAAgB;AAqYtB,eAAeA,gBAAgB;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}