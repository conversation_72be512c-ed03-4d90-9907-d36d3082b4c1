import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import '../styles/SearchSuggestions.css';

const SearchSuggestions = ({ onSelect, placeholder = "Search dishes, categories..." }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState([]);
  const [popularSearches, setPopularSearches] = useState([]);

  const searchRef = useRef(null);
  const suggestionsRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }

    // Load popular searches
    fetchPopularSearches();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (query.length >= 2) {
        fetchSuggestions();
      } else {
        setSuggestions([]);
        setSelectedIndex(-1);
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [query]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchSuggestions = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
      setSuggestions(response.data.suggestions || []);
      setIsOpen(true);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchPopularSearches = async () => {
    try {
      const response = await axios.get('/api/search/popular');
      setPopularSearches(response.data.searches || []);
    } catch (error) {
      console.error('Error fetching popular searches:', error);
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);
    
    if (value.length >= 1) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  };

  const handleInputFocus = () => {
    if (query.length === 0) {
      setIsOpen(true);
    }
  };

  const handleKeyDown = (e) => {
    if (!isOpen) return;

    const totalItems = suggestions.length + (query.length === 0 ? recentSearches.length + popularSearches.length : 0);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < totalItems - 1 ? prev + 1 : -1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > -1 ? prev - 1 : totalItems - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleSuggestionSelect(getSelectedItem());
        } else if (query.trim()) {
          handleSearch(query);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        searchRef.current?.blur();
        break;
    }
  };

  const getSelectedItem = () => {
    if (query.length === 0) {
      const recentCount = recentSearches.length;
      const popularCount = popularSearches.length;
      
      if (selectedIndex < recentCount) {
        return { type: 'recent', text: recentSearches[selectedIndex] };
      } else if (selectedIndex < recentCount + popularCount) {
        return { type: 'popular', text: popularSearches[selectedIndex - recentCount] };
      }
    } else {
      return suggestions[selectedIndex];
    }
    return null;
  };

  const handleSuggestionSelect = (suggestion) => {
    if (!suggestion) return;

    const searchText = suggestion.text || suggestion.name || suggestion;
    setQuery(searchText);
    setIsOpen(false);
    setSelectedIndex(-1);

    // Save to recent searches
    saveToRecentSearches(searchText);

    if (onSelect) {
      onSelect(suggestion);
    } else {
      handleSearch(searchText);
    }
  };

  const handleSearch = (searchText) => {
    saveToRecentSearches(searchText);
    navigate(`/menu?search=${encodeURIComponent(searchText)}`);
  };

  const saveToRecentSearches = (searchText) => {
    const updated = [searchText, ...recentSearches.filter(s => s !== searchText)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };

  const renderSuggestionItem = (item, index, type = 'suggestion') => {
    const isSelected = selectedIndex === index;
    const text = item.text || item.name || item;
    const category = item.category;
    const icon = getTypeIcon(type, item.type);

    return (
      <div
        key={`${type}-${index}`}
        className={`suggestion-item ${isSelected ? 'selected' : ''} ${type}`}
        onClick={() => handleSuggestionSelect(item)}
        onMouseEnter={() => setSelectedIndex(index)}
      >
        <div className="suggestion-icon">{icon}</div>
        <div className="suggestion-content">
          <span className="suggestion-text">{text}</span>
          {category && <span className="suggestion-category">in {category}</span>}
        </div>
        {type === 'recent' && (
          <button 
            className="remove-recent"
            onClick={(e) => {
              e.stopPropagation();
              const updated = recentSearches.filter((_, i) => i !== index);
              setRecentSearches(updated);
              localStorage.setItem('recentSearches', JSON.stringify(updated));
            }}
          >
            ×
          </button>
        )}
      </div>
    );
  };

  const getTypeIcon = (listType, itemType) => {
    if (listType === 'recent') return '🕒';
    if (listType === 'popular') return '🔥';
    
    switch (itemType) {
      case 'dish': return '🍽️';
      case 'category': return '📂';
      case 'ingredient': return '🥬';
      default: return '🔍';
    }
  };

  return (
    <div className="search-suggestions" ref={searchRef}>
      <div className="search-input-container">
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="search-input"
          autoComplete="off"
        />
        <div className="search-icon">🔍</div>
        {loading && <div className="search-loading">⏳</div>}
      </div>

      {isOpen && (
        <div className="suggestions-dropdown" ref={suggestionsRef}>
          {query.length === 0 ? (
            // Show recent and popular searches when no query
            <>
              {recentSearches.length > 0 && (
                <div className="suggestions-section">
                  <div className="section-header">
                    <span>Recent Searches</span>
                    <button onClick={clearRecentSearches} className="clear-recent">
                      Clear All
                    </button>
                  </div>
                  {recentSearches.map((search, index) => 
                    renderSuggestionItem(search, index, 'recent')
                  )}
                </div>
              )}
              
              {popularSearches.length > 0 && (
                <div className="suggestions-section">
                  <div className="section-header">
                    <span>Popular Searches</span>
                  </div>
                  {popularSearches.slice(0, 5).map((search, index) => 
                    renderSuggestionItem(search, recentSearches.length + index, 'popular')
                  )}
                </div>
              )}
            </>
          ) : (
            // Show search suggestions
            <>
              {suggestions.length > 0 ? (
                <div className="suggestions-section">
                  <div className="section-header">
                    <span>Suggestions</span>
                  </div>
                  {suggestions.map((suggestion, index) => 
                    renderSuggestionItem(suggestion, index, 'suggestion')
                  )}
                </div>
              ) : !loading && (
                <div className="no-suggestions">
                  <span>No suggestions found</span>
                  <button 
                    onClick={() => handleSearch(query)}
                    className="search-anyway"
                  >
                    Search for "{query}" anyway
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchSuggestions;
