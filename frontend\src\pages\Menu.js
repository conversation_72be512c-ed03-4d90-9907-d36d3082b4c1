import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useCart } from '../context/CartContext';
import { toast } from 'react-toastify';
import MenuItemModal from '../components/MenuItemModal';
import { QuickRating } from '../components/Rating';
import '../styles/Menu.css';

const Menu = () => {
  const [dishes, setDishes] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    category: 'all',
    search: '',
    dietary: '',
    minPrice: '',
    maxPrice: '',
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0
  });
  const [selectedDish, setSelectedDish] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { addToCart, getItemQuantity } = useCart();

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchDishes();
  }, [filters, pagination.page]);

  const fetchCategories = async () => {
    try {
      const response = await axios.get('/api/menu/categories');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchDishes = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      });

      // Remove empty filters
      Object.keys(filters).forEach(key => {
        if (!filters[key] || filters[key] === 'all') {
          params.delete(key);
        }
      });

      const response = await axios.get(`/api/menu?${params}`);
      setDishes(response.data.dishes);
      setPagination(prev => ({
        ...prev,
        ...response.data.pagination
      }));
    } catch (error) {
      console.error('Error fetching dishes:', error);
      toast.error('Failed to load menu items');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleAddToCart = (dish) => {
    addToCart(dish, 1);
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDishClick = (dish) => {
    setSelectedDish(dish);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedDish(null);
  };

  return (
    <div className="menu-page">
      <div className="container">
        <div className="menu-header">
          <h1>Our Menu</h1>
          <p>Discover our delicious selection of carefully crafted dishes</p>
        </div>

        {/* Filters */}
        <div className="menu-filters">
          <div className="filter-group">
            <label>Category:</label>
            <select 
              value={filters.category} 
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Search:</label>
            <input
              type="text"
              placeholder="Search dishes..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <div className="filter-group">
            <label>Dietary:</label>
            <select 
              value={filters.dietary} 
              onChange={(e) => handleFilterChange('dietary', e.target.value)}
            >
              <option value="">All</option>
              <option value="vegetarian">Vegetarian</option>
              <option value="vegan">Vegan</option>
              <option value="gluten-free">Gluten Free</option>
              <option value="dairy-free">Dairy Free</option>
              <option value="keto">Keto</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Price Range:</label>
            <div className="price-inputs">
              <input
                type="number"
                placeholder="Min"
                value={filters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
              />
              <input
                type="number"
                placeholder="Max"
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
              />
            </div>
          </div>

          <div className="filter-group">
            <label>Sort by:</label>
            <select 
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                handleFilterChange('sortBy', sortBy);
                handleFilterChange('sortOrder', sortOrder);
              }}
            >
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="price-asc">Price (Low to High)</option>
              <option value="price-desc">Price (High to Low)</option>
              <option value="rating-desc">Rating (High to Low)</option>
            </select>
          </div>
        </div>

        {/* Menu Items */}
        {loading ? (
          <div className="loading">Loading menu items...</div>
        ) : (
          <>
            <div className="menu-grid">
              {dishes.map((dish) => (
                <div key={dish._id} className="menu-item">
                  <div className="menu-item-image" onClick={() => handleDishClick(dish)}>
                    <img
                      src={`http://localhost:5000/uploads/${dish.image}`}
                      alt={dish.name}
                      onError={(e) => {
                        e.target.src = '/api/placeholder/300/200';
                      }}
                    />
                    {dish.isPopular && <span className="popular-badge">Popular</span>}
                    <div className="image-overlay">
                      <span>View Details</span>
                    </div>
                  </div>

                  <div className="menu-item-content">
                    <h3 onClick={() => handleDishClick(dish)}>{dish.name}</h3>
                    <p className="description">{dish.description}</p>

                    <div className="dish-meta">
                      <span className="price">${dish.price}</span>
                      <span className="prep-time">⏱️ {dish.preparationTime} min</span>
                      {dish.rating > 0 && (
                        <div className="rating-display">
                          <QuickRating value={dish.rating} size="small" />
                          <span className="rating-count">({dish.reviewCount || 0})</span>
                        </div>
                      )}
                    </div>

                    {dish.dietaryInfo && dish.dietaryInfo.length > 0 && (
                      <div className="dietary-tags">
                        {dish.dietaryInfo.map((tag) => (
                          <span key={tag} className="dietary-tag">{tag}</span>
                        ))}
                      </div>
                    )}

                    {dish.spiceLevel && dish.spiceLevel !== 'mild' && (
                      <div className="spice-level">
                        🌶️ {dish.spiceLevel}
                      </div>
                    )}

                    <div className="menu-item-actions">
                      <button 
                        className="add-to-cart-btn"
                        onClick={() => handleAddToCart(dish)}
                        disabled={!dish.isAvailable}
                      >
                        {!dish.isAvailable ? 'Unavailable' : 
                         getItemQuantity(dish._id) > 0 ? 
                         `In Cart (${getItemQuantity(dish._id)})` : 
                         'Add to Cart'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="pagination">
                <button 
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                  className="pagination-btn"
                >
                  Previous
                </button>
                
                <div className="pagination-info">
                  Page {pagination.page} of {pagination.pages}
                </div>
                
                <button 
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages}
                  className="pagination-btn"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Menu Item Modal */}
      <MenuItemModal
        dish={selectedDish}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default Menu;
