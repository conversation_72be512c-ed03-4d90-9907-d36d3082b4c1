import React, { useState, useEffect } from 'react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import Rating, { QuickRating } from './Rating';
import ReviewsList from './ReviewsList';
import ReviewForm from './ReviewForm';
import { toast } from 'react-toastify';
import '../styles/MenuItemModal.css';

const MenuItemModal = ({ dish, isOpen, onClose }) => {
  const [quantity, setQuantity] = useState(1);
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [activeTab, setActiveTab] = useState('details');
  const [selectedImage, setSelectedImage] = useState(0);
  const [showReviewForm, setShowReviewForm] = useState(false);

  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isOpen && dish) {
      setQuantity(1);
      setSpecialInstructions('');
      setActiveTab('details');
      setSelectedImage(0);
      setShowReviewForm(false);
    }
  }, [isOpen, dish]);

  const handleAddToCart = () => {
    if (!dish.isAvailable) {
      toast.error('This item is currently unavailable');
      return;
    }

    addToCart(dish, quantity, specialInstructions);
    toast.success(`${dish.name} added to cart!`);
    onClose();
  };

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity);
    }
  };

  const handleImageSelect = (index) => {
    setSelectedImage(index);
  };

  const handleReviewSubmitted = () => {
    setShowReviewForm(false);
    setActiveTab('reviews');
    toast.success('Review submitted successfully!');
  };

  if (!isOpen || !dish) return null;

  const images = dish.images && dish.images.length > 0 ? dish.images : [dish.image];

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="menu-item-modal" onClick={(e) => e.stopPropagation()}>
        <button className="modal-close" onClick={onClose}>×</button>
        
        <div className="modal-content">
          {/* Image Gallery */}
          <div className="image-gallery">
            <div className="main-image">
              <img 
                src={`http://localhost:5000/uploads/${images[selectedImage]}`}
                alt={dish.name}
                onError={(e) => {
                  e.target.src = '/api/placeholder/600/400';
                }}
              />
              {!dish.isAvailable && (
                <div className="unavailable-overlay">
                  <span>Currently Unavailable</span>
                </div>
              )}
            </div>
            
            {images.length > 1 && (
              <div className="image-thumbnails">
                {images.map((image, index) => (
                  <img
                    key={index}
                    src={`http://localhost:5000/uploads/${image}`}
                    alt={`${dish.name} ${index + 1}`}
                    className={`thumbnail ${selectedImage === index ? 'active' : ''}`}
                    onClick={() => handleImageSelect(index)}
                    onError={(e) => {
                      e.target.src = '/api/placeholder/100/100';
                    }}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Dish Info */}
          <div className="dish-info">
            <div className="dish-header">
              <h2>{dish.name}</h2>
              <div className="dish-meta">
                <span className="price">${dish.price}</span>
                {dish.rating > 0 && (
                  <div className="rating-display">
                    <QuickRating value={dish.rating} size="small" />
                    <span className="rating-count">({dish.reviewCount || 0} reviews)</span>
                  </div>
                )}
              </div>
            </div>

            <p className="description">{dish.description}</p>

            {/* Dish Details */}
            <div className="dish-details">
              {dish.preparationTime && (
                <div className="detail-item">
                  <span className="label">Prep Time:</span>
                  <span className="value">{dish.preparationTime} min</span>
                </div>
              )}
              
              {dish.calories && (
                <div className="detail-item">
                  <span className="label">Calories:</span>
                  <span className="value">{dish.calories}</span>
                </div>
              )}
              
              {dish.spiceLevel && dish.spiceLevel !== 'mild' && (
                <div className="detail-item">
                  <span className="label">Spice Level:</span>
                  <span className="value spice-level">{dish.spiceLevel}</span>
                </div>
              )}
            </div>

            {/* Dietary Info */}
            {dish.dietaryInfo && dish.dietaryInfo.length > 0 && (
              <div className="dietary-tags">
                {dish.dietaryInfo.map((tag) => (
                  <span key={tag} className="dietary-tag">{tag}</span>
                ))}
              </div>
            )}

            {/* Ingredients */}
            {dish.ingredients && dish.ingredients.length > 0 && (
              <div className="ingredients">
                <h4>Ingredients:</h4>
                <p>{dish.ingredients.join(', ')}</p>
              </div>
            )}

            {/* Allergens */}
            {dish.allergens && dish.allergens.length > 0 && (
              <div className="allergens">
                <h4>Allergens:</h4>
                <div className="allergen-tags">
                  {dish.allergens.map((allergen) => (
                    <span key={allergen} className="allergen-tag">{allergen}</span>
                  ))}
                </div>
              </div>
            )}

            {/* Order Section */}
            <div className="order-section">
              <div className="quantity-selector">
                <label>Quantity:</label>
                <div className="quantity-controls">
                  <button 
                    onClick={() => handleQuantityChange(-1)}
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <span className="quantity">{quantity}</span>
                  <button 
                    onClick={() => handleQuantityChange(1)}
                    disabled={quantity >= 10}
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="special-instructions">
                <label htmlFor="instructions">Special Instructions:</label>
                <textarea
                  id="instructions"
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  placeholder="Any special requests or modifications..."
                  maxLength={200}
                />
                <small>{specialInstructions.length}/200 characters</small>
              </div>

              <div className="order-total">
                <span className="total-label">Total:</span>
                <span className="total-price">${(dish.price * quantity).toFixed(2)}</span>
              </div>

              <button 
                className="add-to-cart-btn"
                onClick={handleAddToCart}
                disabled={!dish.isAvailable}
              >
                {!dish.isAvailable ? 'Unavailable' : `Add ${quantity} to Cart`}
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="modal-tabs">
          <button 
            className={`tab ${activeTab === 'details' ? 'active' : ''}`}
            onClick={() => setActiveTab('details')}
          >
            Details
          </button>
          <button 
            className={`tab ${activeTab === 'reviews' ? 'active' : ''}`}
            onClick={() => setActiveTab('reviews')}
          >
            Reviews ({dish.reviewCount || 0})
          </button>
          <button 
            className={`tab ${activeTab === 'nutrition' ? 'active' : ''}`}
            onClick={() => setActiveTab('nutrition')}
          >
            Nutrition
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'details' && (
            <div className="details-tab">
              <h3>About This Dish</h3>
              <p>{dish.longDescription || dish.description}</p>
              
              {dish.chefNotes && (
                <div className="chef-notes">
                  <h4>Chef's Notes:</h4>
                  <p>{dish.chefNotes}</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="reviews-tab">
              {isAuthenticated && !showReviewForm && (
                <button 
                  className="write-review-btn"
                  onClick={() => setShowReviewForm(true)}
                >
                  Write a Review
                </button>
              )}
              
              {showReviewForm && (
                <ReviewForm 
                  dishId={dish._id}
                  dishName={dish.name}
                  onSubmit={handleReviewSubmitted}
                  onCancel={() => setShowReviewForm(false)}
                />
              )}
              
              <ReviewsList 
                dishId={dish._id} 
                dishName={dish.name}
              />
            </div>
          )}

          {activeTab === 'nutrition' && (
            <div className="nutrition-tab">
              <h3>Nutritional Information</h3>
              <div className="nutrition-grid">
                {dish.calories && (
                  <div className="nutrition-item">
                    <span className="label">Calories</span>
                    <span className="value">{dish.calories}</span>
                  </div>
                )}
                {dish.protein && (
                  <div className="nutrition-item">
                    <span className="label">Protein</span>
                    <span className="value">{dish.protein}g</span>
                  </div>
                )}
                {dish.carbs && (
                  <div className="nutrition-item">
                    <span className="label">Carbohydrates</span>
                    <span className="value">{dish.carbs}g</span>
                  </div>
                )}
                {dish.fat && (
                  <div className="nutrition-item">
                    <span className="label">Fat</span>
                    <span className="value">{dish.fat}g</span>
                  </div>
                )}
                {dish.fiber && (
                  <div className="nutrition-item">
                    <span className="label">Fiber</span>
                    <span className="value">{dish.fiber}g</span>
                  </div>
                )}
                {dish.sodium && (
                  <div className="nutrition-item">
                    <span className="label">Sodium</span>
                    <span className="value">{dish.sodium}mg</span>
                  </div>
                )}
              </div>
              
              {(!dish.calories && !dish.protein && !dish.carbs && !dish.fat) && (
                <p className="no-nutrition">Nutritional information not available for this item.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MenuItemModal;
