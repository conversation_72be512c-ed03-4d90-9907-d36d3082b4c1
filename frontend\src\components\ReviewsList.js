import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useAuth } from '../context/AuthContext';
import '../styles/ReviewsList.css';

const ReviewsList = ({ dishId, dishName }) => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [ratingStats, setRatingStats] = useState([]);
  const [filters, setFilters] = useState({
    rating: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    fetchReviews();
  }, [dishId, filters, pagination.page]);

  const fetchReviews = async () => {
    try {
      const params = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      });

      if (filters.rating) {
        params.append('rating', filters.rating);
      }

      const response = await axios.get(`/api/reviews/dish/${dishId}?${params}`);
      setReviews(response.data.reviews);
      setPagination(prev => ({
        ...prev,
        ...response.data.pagination
      }));
      setRatingStats(response.data.ratingStats);
    } catch (error) {
      toast.error('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleVote = async (reviewId, voteType) => {
    if (!isAuthenticated) {
      toast.info('Please login to vote on reviews');
      return;
    }

    try {
      await axios.post(`/api/reviews/${reviewId}/vote`, { voteType });
      toast.success('Vote recorded!');
      fetchReviews(); // Refresh reviews
    } catch (error) {
      toast.error('Failed to record vote');
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={`star ${index < rating ? 'filled' : ''}`}>
        ⭐
      </span>
    ));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateAverageRating = () => {
    if (ratingStats.length === 0) return 0;
    const totalRatings = ratingStats.reduce((sum, stat) => sum + stat.count, 0);
    const weightedSum = ratingStats.reduce((sum, stat) => sum + (stat._id * stat.count), 0);
    return totalRatings > 0 ? (weightedSum / totalRatings).toFixed(1) : 0;
  };

  const getTotalReviews = () => {
    return ratingStats.reduce((sum, stat) => sum + stat.count, 0);
  };

  if (loading) {
    return <div className="reviews-loading">Loading reviews...</div>;
  }

  return (
    <div className="reviews-section">
      <div className="reviews-header">
        <h3>Customer Reviews for {dishName}</h3>
        
        {ratingStats.length > 0 && (
          <div className="rating-summary">
            <div className="average-rating">
              <span className="rating-number">{calculateAverageRating()}</span>
              <div className="rating-stars">
                {renderStars(Math.round(calculateAverageRating()))}
              </div>
              <span className="total-reviews">({getTotalReviews()} reviews)</span>
            </div>
            
            <div className="rating-breakdown">
              {[5, 4, 3, 2, 1].map(rating => {
                const stat = ratingStats.find(s => s._id === rating);
                const count = stat ? stat.count : 0;
                const percentage = getTotalReviews() > 0 ? (count / getTotalReviews()) * 100 : 0;
                
                return (
                  <div key={rating} className="rating-bar">
                    <span className="rating-label">{rating} ⭐</span>
                    <div className="bar-container">
                      <div 
                        className="bar-fill" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="rating-count">({count})</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      <div className="reviews-filters">
        <div className="filter-group">
          <label>Filter by Rating:</label>
          <select 
            value={filters.rating} 
            onChange={(e) => handleFilterChange('rating', e.target.value)}
          >
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Sort by:</label>
          <select 
            value={`${filters.sortBy}-${filters.sortOrder}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleFilterChange('sortBy', sortBy);
              handleFilterChange('sortOrder', sortOrder);
            }}
          >
            <option value="createdAt-desc">Newest First</option>
            <option value="createdAt-asc">Oldest First</option>
            <option value="rating-desc">Highest Rated</option>
            <option value="rating-asc">Lowest Rated</option>
            <option value="helpfulVotes-desc">Most Helpful</option>
          </select>
        </div>
      </div>

      <div className="reviews-list">
        {reviews.length === 0 ? (
          <div className="no-reviews">
            <p>No reviews yet for this dish.</p>
            <p>Be the first to share your experience!</p>
          </div>
        ) : (
          reviews.map((review) => (
            <div key={review._id} className="review-item">
              <div className="review-header">
                <div className="reviewer-info">
                  <span className="reviewer-name">{review.customer.name}</span>
                  <span className="review-date">{formatDate(review.createdAt)}</span>
                  {review.isVerifiedPurchase && (
                    <span className="verified-badge">✓ Verified Purchase</span>
                  )}
                </div>
                <div className="review-rating">
                  {renderStars(review.rating)}
                </div>
              </div>

              <div className="review-content">
                <h4 className="review-title">{review.title}</h4>
                <p className="review-comment">{review.comment}</p>
                
                {review.images && review.images.length > 0 && (
                  <div className="review-images">
                    {review.images.map((image, index) => (
                      <img 
                        key={index}
                        src={`http://localhost:5000/uploads/${image}`}
                        alt={`Review image ${index + 1}`}
                        className="review-image"
                        onClick={() => window.open(`http://localhost:5000/uploads/${image}`, '_blank')}
                      />
                    ))}
                  </div>
                )}
              </div>

              <div className="review-actions">
                <div className="helpfulness">
                  <span>Was this helpful?</span>
                  <button 
                    className="vote-btn helpful"
                    onClick={() => handleVote(review._id, 'helpful')}
                    disabled={!isAuthenticated}
                  >
                    👍 Yes ({review.helpfulVotes})
                  </button>
                  <button 
                    className="vote-btn not-helpful"
                    onClick={() => handleVote(review._id, 'not-helpful')}
                    disabled={!isAuthenticated}
                  >
                    👎 No
                  </button>
                </div>
              </div>

              {review.adminResponse && (
                <div className="admin-response">
                  <div className="response-header">
                    <strong>Restaurant Response:</strong>
                    <span className="response-date">
                      {formatDate(review.adminResponse.respondedAt)}
                    </span>
                  </div>
                  <p className="response-message">{review.adminResponse.message}</p>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {pagination.pages > 1 && (
        <div className="reviews-pagination">
          <button 
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            className="pagination-btn"
          >
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.page} of {pagination.pages}
          </span>
          
          <button 
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page === pagination.pages}
            className="pagination-btn"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default ReviewsList;
