const mongoose = require('mongoose');

const loyaltyTransactionSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['earned', 'redeemed', 'expired', 'bonus'],
    required: true
  },
  points: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  expiresAt: {
    type: Date
  }
}, {
  timestamps: true
});

const loyaltyProgramSchema = new mongoose.Schema({
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  totalPoints: {
    type: Number,
    default: 0,
    min: 0
  },
  availablePoints: {
    type: Number,
    default: 0,
    min: 0
  },
  lifetimePoints: {
    type: Number,
    default: 0,
    min: 0
  },
  tier: {
    type: String,
    enum: ['bronze', 'silver', 'gold', 'platinum'],
    default: 'bronze'
  },
  tierProgress: {
    currentSpend: {
      type: Number,
      default: 0
    },
    nextTierSpend: {
      type: Number,
      default: 500 // Bronze to Silver
    },
    nextTier: {
      type: String,
      default: 'silver'
    }
  },
  transactions: [loyaltyTransactionSchema],
  achievements: [{
    type: {
      type: String,
      enum: ['first_order', 'frequent_diner', 'big_spender', 'reviewer', 'referrer']
    },
    title: String,
    description: String,
    pointsAwarded: Number,
    unlockedAt: {
      type: Date,
      default: Date.now
    }
  }],
  referralCode: {
    type: String,
    unique: true,
    sparse: true
  },
  referredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  referrals: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    pointsAwarded: Number,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes
loyaltyProgramSchema.index({ customer: 1 });
loyaltyProgramSchema.index({ tier: 1 });
loyaltyProgramSchema.index({ totalPoints: -1 });

// Virtual for points expiring soon
loyaltyProgramSchema.virtual('pointsExpiringSoon').get(function() {
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
  
  return this.transactions
    .filter(t => t.type === 'earned' && t.expiresAt && t.expiresAt <= thirtyDaysFromNow)
    .reduce((sum, t) => sum + t.points, 0);
});

// Method to calculate tier based on lifetime spend
loyaltyProgramSchema.methods.calculateTier = function() {
  const lifetimeSpend = this.tierProgress.currentSpend;
  
  if (lifetimeSpend >= 2000) {
    this.tier = 'platinum';
    this.tierProgress.nextTier = null;
    this.tierProgress.nextTierSpend = null;
  } else if (lifetimeSpend >= 1000) {
    this.tier = 'gold';
    this.tierProgress.nextTier = 'platinum';
    this.tierProgress.nextTierSpend = 2000;
  } else if (lifetimeSpend >= 500) {
    this.tier = 'silver';
    this.tierProgress.nextTier = 'gold';
    this.tierProgress.nextTierSpend = 1000;
  } else {
    this.tier = 'bronze';
    this.tierProgress.nextTier = 'silver';
    this.tierProgress.nextTierSpend = 500;
  }
};

// Method to add points
loyaltyProgramSchema.methods.addPoints = function(points, description, orderId = null) {
  const expiresAt = new Date();
  expiresAt.setFullYear(expiresAt.getFullYear() + 1); // Points expire in 1 year
  
  this.transactions.push({
    type: 'earned',
    points,
    description,
    order: orderId,
    expiresAt
  });
  
  this.totalPoints += points;
  this.availablePoints += points;
  this.lifetimePoints += points;
};

// Method to redeem points
loyaltyProgramSchema.methods.redeemPoints = function(points, description) {
  if (this.availablePoints < points) {
    throw new Error('Insufficient points');
  }
  
  this.transactions.push({
    type: 'redeemed',
    points: -points,
    description
  });
  
  this.availablePoints -= points;
};

// Method to add achievement
loyaltyProgramSchema.methods.addAchievement = function(type, title, description, pointsAwarded) {
  // Check if achievement already exists
  const existingAchievement = this.achievements.find(a => a.type === type);
  if (existingAchievement) {
    return false;
  }
  
  this.achievements.push({
    type,
    title,
    description,
    pointsAwarded
  });
  
  if (pointsAwarded > 0) {
    this.addPoints(pointsAwarded, `Achievement: ${title}`);
  }
  
  return true;
};

// Static method to get tier benefits
loyaltyProgramSchema.statics.getTierBenefits = function(tier) {
  const benefits = {
    bronze: {
      pointsMultiplier: 1,
      discountPercentage: 0,
      freeDelivery: false,
      prioritySupport: false,
      birthdayBonus: 50
    },
    silver: {
      pointsMultiplier: 1.25,
      discountPercentage: 5,
      freeDelivery: false,
      prioritySupport: false,
      birthdayBonus: 100
    },
    gold: {
      pointsMultiplier: 1.5,
      discountPercentage: 10,
      freeDelivery: true,
      prioritySupport: true,
      birthdayBonus: 200
    },
    platinum: {
      pointsMultiplier: 2,
      discountPercentage: 15,
      freeDelivery: true,
      prioritySupport: true,
      birthdayBonus: 500
    }
  };
  
  return benefits[tier] || benefits.bronze;
};

// Generate referral code
loyaltyProgramSchema.methods.generateReferralCode = function() {
  if (!this.referralCode) {
    const code = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.referralCode = `REF${code}`;
  }
  return this.referralCode;
};

module.exports = mongoose.model('LoyaltyProgram', loyaltyProgramSchema);
