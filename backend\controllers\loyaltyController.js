const LoyaltyProgram = require('../models/LoyaltyProgram');
const User = require('../models/User');

// @desc    Get user's loyalty program details
// @route   GET /api/loyalty/profile
// @access  Private
const getLoyaltyProfile = async (req, res) => {
  try {
    let loyaltyProgram = await LoyaltyProgram.findOne({ customer: req.user._id })
      .populate('referredBy', 'name')
      .populate('referrals.user', 'name');

    // Create loyalty program if it doesn't exist
    if (!loyaltyProgram) {
      loyaltyProgram = await LoyaltyProgram.create({
        customer: req.user._id
      });
      loyaltyProgram.generateReferralCode();
      await loyaltyProgram.save();
    }

    // Get tier benefits
    const tierBenefits = LoyaltyProgram.getTierBenefits(loyaltyProgram.tier);

    res.json({
      loyaltyProgram,
      tierBenefits,
      pointsExpiringSoon: loyaltyProgram.pointsExpiringSoon
    });
  } catch (error) {
    console.error('Get loyalty profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get loyalty transactions
// @route   GET /api/loyalty/transactions
// @access  Private
const getLoyaltyTransactions = async (req, res) => {
  try {
    const { page = 1, limit = 20, type } = req.query;
    
    const loyaltyProgram = await LoyaltyProgram.findOne({ customer: req.user._id });
    
    if (!loyaltyProgram) {
      return res.status(404).json({ message: 'Loyalty program not found' });
    }

    let transactions = loyaltyProgram.transactions;
    
    // Filter by type if specified
    if (type) {
      transactions = transactions.filter(t => t.type === type);
    }

    // Sort by date (newest first)
    transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    
    const paginatedTransactions = transactions.slice(startIndex, endIndex);
    const total = transactions.length;

    res.json({
      transactions: paginatedTransactions,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get loyalty transactions error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Redeem points for discount
// @route   POST /api/loyalty/redeem
// @access  Private
const redeemPoints = async (req, res) => {
  try {
    const { points, rewardType } = req.body;
    
    const loyaltyProgram = await LoyaltyProgram.findOne({ customer: req.user._id });
    
    if (!loyaltyProgram) {
      return res.status(404).json({ message: 'Loyalty program not found' });
    }

    // Validate redemption
    const minRedemption = 100;
    if (points < minRedemption) {
      return res.status(400).json({ 
        message: `Minimum redemption is ${minRedemption} points` 
      });
    }

    if (loyaltyProgram.availablePoints < points) {
      return res.status(400).json({ message: 'Insufficient points' });
    }

    // Calculate discount value (1 point = $0.01)
    const discountValue = points * 0.01;
    
    // Create redemption record
    loyaltyProgram.redeemPoints(points, `Redeemed for $${discountValue.toFixed(2)} discount`);
    await loyaltyProgram.save();

    // Generate discount code
    const discountCode = `LOYALTY${Date.now()}`;
    
    res.json({
      message: 'Points redeemed successfully',
      discountCode,
      discountValue: discountValue.toFixed(2),
      remainingPoints: loyaltyProgram.availablePoints
    });
  } catch (error) {
    console.error('Redeem points error:', error);
    res.status(500).json({ message: error.message || 'Server error' });
  }
};

// @desc    Apply referral code
// @route   POST /api/loyalty/referral
// @access  Private
const applyReferralCode = async (req, res) => {
  try {
    const { referralCode } = req.body;
    
    // Find the referrer
    const referrerProgram = await LoyaltyProgram.findOne({ referralCode });
    
    if (!referrerProgram) {
      return res.status(404).json({ message: 'Invalid referral code' });
    }

    // Check if user is trying to refer themselves
    if (referrerProgram.customer.toString() === req.user._id.toString()) {
      return res.status(400).json({ message: 'You cannot refer yourself' });
    }

    // Get or create user's loyalty program
    let userProgram = await LoyaltyProgram.findOne({ customer: req.user._id });
    
    if (!userProgram) {
      userProgram = await LoyaltyProgram.create({
        customer: req.user._id,
        referredBy: referrerProgram.customer
      });
    } else if (userProgram.referredBy) {
      return res.status(400).json({ message: 'You have already used a referral code' });
    } else {
      userProgram.referredBy = referrerProgram.customer;
    }

    // Award points to both users
    const referralBonus = 100;
    
    // Award points to new user
    userProgram.addPoints(referralBonus, 'Referral bonus - Welcome!');
    await userProgram.save();

    // Award points to referrer
    referrerProgram.addPoints(referralBonus, `Referral bonus - Referred ${req.user.name}`);
    referrerProgram.referrals.push({
      user: req.user._id,
      pointsAwarded: referralBonus
    });
    await referrerProgram.save();

    res.json({
      message: 'Referral code applied successfully!',
      pointsAwarded: referralBonus
    });
  } catch (error) {
    console.error('Apply referral code error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get leaderboard
// @route   GET /api/loyalty/leaderboard
// @access  Public
const getLeaderboard = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const leaderboard = await LoyaltyProgram.find()
      .populate('customer', 'name')
      .sort({ lifetimePoints: -1 })
      .limit(parseInt(limit))
      .select('customer lifetimePoints tier');

    res.json(leaderboard);
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Award points for order (Internal function)
const awardPointsForOrder = async (userId, orderAmount, orderId) => {
  try {
    let loyaltyProgram = await LoyaltyProgram.findOne({ customer: userId });
    
    if (!loyaltyProgram) {
      loyaltyProgram = await LoyaltyProgram.create({ customer: userId });
    }

    // Calculate points (1 point per dollar spent)
    const basePoints = Math.floor(orderAmount);
    
    // Apply tier multiplier
    const tierBenefits = LoyaltyProgram.getTierBenefits(loyaltyProgram.tier);
    const totalPoints = Math.floor(basePoints * tierBenefits.pointsMultiplier);
    
    // Award points
    loyaltyProgram.addPoints(totalPoints, `Order #${orderId}`, orderId);
    
    // Update tier progress
    loyaltyProgram.tierProgress.currentSpend += orderAmount;
    loyaltyProgram.calculateTier();
    
    // Check for achievements
    const orderCount = await require('../models/Order').countDocuments({ 
      customer: userId, 
      status: 'delivered' 
    });
    
    if (orderCount === 1) {
      loyaltyProgram.addAchievement(
        'first_order',
        'First Order',
        'Completed your first order',
        50
      );
    } else if (orderCount === 10) {
      loyaltyProgram.addAchievement(
        'frequent_diner',
        'Frequent Diner',
        'Completed 10 orders',
        200
      );
    }
    
    if (orderAmount >= 100) {
      loyaltyProgram.addAchievement(
        'big_spender',
        'Big Spender',
        'Placed an order over $100',
        100
      );
    }
    
    await loyaltyProgram.save();
    
    return {
      pointsAwarded: totalPoints,
      newTier: loyaltyProgram.tier,
      achievements: loyaltyProgram.achievements.filter(a => 
        new Date(a.unlockedAt) > new Date(Date.now() - 60000) // Last minute
      )
    };
  } catch (error) {
    console.error('Award points for order error:', error);
    throw error;
  }
};

// @desc    Get loyalty statistics (Admin)
// @route   GET /api/loyalty/admin/stats
// @access  Private/Admin
const getLoyaltyStats = async (req, res) => {
  try {
    const totalMembers = await LoyaltyProgram.countDocuments();
    
    const tierDistribution = await LoyaltyProgram.aggregate([
      { $group: { _id: '$tier', count: { $sum: 1 } } }
    ]);
    
    const totalPointsIssued = await LoyaltyProgram.aggregate([
      { $group: { _id: null, total: { $sum: '$lifetimePoints' } } }
    ]);
    
    const totalPointsRedeemed = await LoyaltyProgram.aggregate([
      { $unwind: '$transactions' },
      { $match: { 'transactions.type': 'redeemed' } },
      { $group: { _id: null, total: { $sum: { $abs: '$transactions.points' } } } }
    ]);

    res.json({
      totalMembers,
      tierDistribution,
      totalPointsIssued: totalPointsIssued[0]?.total || 0,
      totalPointsRedeemed: totalPointsRedeemed[0]?.total || 0
    });
  } catch (error) {
    console.error('Get loyalty stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getLoyaltyProfile,
  getLoyaltyTransactions,
  redeemPoints,
  applyReferralCode,
  getLeaderboard,
  awardPointsForOrder,
  getLoyaltyStats
};
