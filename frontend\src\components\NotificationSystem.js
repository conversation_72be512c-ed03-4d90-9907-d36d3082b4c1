import React, { useState, useEffect, useContext, createContext } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from '../context/AuthContext';
import '../styles/NotificationSystem.css';

const NotificationContext = createContext();

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Initialize socket connection
      const newSocket = io(process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000', {
        auth: {
          token: localStorage.getItem('token')
        }
      });

      newSocket.on('connect', () => {
        setIsConnected(true);
        console.log('Connected to notification server');
      });

      newSocket.on('disconnect', () => {
        setIsConnected(false);
        console.log('Disconnected from notification server');
      });

      // Listen for different types of notifications
      newSocket.on('orderUpdate', (data) => {
        addNotification({
          type: 'order',
          title: 'Order Update',
          message: `Your order #${data.orderId} is now ${data.status}`,
          data: data,
          priority: 'high'
        });
      });

      newSocket.on('reservationUpdate', (data) => {
        addNotification({
          type: 'reservation',
          title: 'Reservation Update',
          message: `Your reservation for ${data.date} has been ${data.status}`,
          data: data,
          priority: 'medium'
        });
      });

      newSocket.on('promotion', (data) => {
        addNotification({
          type: 'promotion',
          title: 'Special Offer!',
          message: data.message,
          data: data,
          priority: 'low'
        });
      });

      newSocket.on('billSplitRequest', (data) => {
        addNotification({
          type: 'billSplit',
          title: 'Bill Split Request',
          message: `${data.requesterName} wants to split a bill with you`,
          data: data,
          priority: 'high',
          actions: [
            { label: 'Accept', action: () => handleBillSplitResponse(data.requestId, 'accept') },
            { label: 'Decline', action: () => handleBillSplitResponse(data.requestId, 'decline') }
          ]
        });
      });

      newSocket.on('reviewResponse', (data) => {
        addNotification({
          type: 'review',
          title: 'Review Response',
          message: `The restaurant responded to your review`,
          data: data,
          priority: 'medium'
        });
      });

      setSocket(newSocket);

      return () => {
        newSocket.close();
      };
    }
  }, [isAuthenticated, user]);

  const addNotification = (notification) => {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      timestamp: new Date(),
      read: false,
      ...notification
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Auto-remove low priority notifications after 5 seconds
    if (notification.priority === 'low') {
      setTimeout(() => {
        removeNotification(id);
      }, 5000);
    }

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.type
      });
    }
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const handleBillSplitResponse = (requestId, response) => {
    if (socket) {
      socket.emit('billSplitResponse', { requestId, response });
    }
    // Remove the notification after responding
    setNotifications(prev => 
      prev.filter(n => !(n.type === 'billSplit' && n.data.requestId === requestId))
    );
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      await Notification.requestPermission();
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const value = {
    notifications,
    unreadCount,
    isConnected,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    requestNotificationPermission
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

const NotificationItem = ({ notification, onRemove, onMarkAsRead }) => {
  const getIcon = (type) => {
    switch (type) {
      case 'order': return '🍽️';
      case 'reservation': return '📅';
      case 'promotion': return '🎉';
      case 'billSplit': return '💰';
      case 'review': return '⭐';
      default: return '🔔';
    }
  };

  const getPriorityClass = (priority) => {
    switch (priority) {
      case 'high': return 'priority-high';
      case 'medium': return 'priority-medium';
      case 'low': return 'priority-low';
      default: return '';
    }
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const diff = now - new Date(timestamp);
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div 
      className={`notification-item ${!notification.read ? 'unread' : ''} ${getPriorityClass(notification.priority)}`}
      onClick={() => !notification.read && onMarkAsRead(notification.id)}
    >
      <div className="notification-icon">
        {getIcon(notification.type)}
      </div>
      
      <div className="notification-content">
        <div className="notification-header">
          <h4>{notification.title}</h4>
          <span className="notification-time">{formatTime(notification.timestamp)}</span>
        </div>
        <p>{notification.message}</p>
        
        {notification.actions && (
          <div className="notification-actions">
            {notification.actions.map((action, index) => (
              <button
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  action.action();
                }}
                className={`action-btn ${index === 0 ? 'primary' : 'secondary'}`}
              >
                {action.label}
              </button>
            ))}
          </div>
        )}
      </div>
      
      <button 
        className="notification-close"
        onClick={(e) => {
          e.stopPropagation();
          onRemove(notification.id);
        }}
      >
        ×
      </button>
    </div>
  );
};

const NotificationDropdown = ({ isOpen, onClose }) => {
  const { notifications, unreadCount, markAsRead, removeNotification, markAllAsRead, clearAll } = useNotifications();

  if (!isOpen) return null;

  return (
    <div className="notification-dropdown">
      <div className="notification-header">
        <h3>Notifications</h3>
        <div className="notification-controls">
          {unreadCount > 0 && (
            <button onClick={markAllAsRead} className="mark-all-read">
              Mark all read
            </button>
          )}
          {notifications.length > 0 && (
            <button onClick={clearAll} className="clear-all">
              Clear all
            </button>
          )}
        </div>
      </div>
      
      <div className="notification-list">
        {notifications.length === 0 ? (
          <div className="no-notifications">
            <span>🔔</span>
            <p>No notifications yet</p>
          </div>
        ) : (
          notifications.map(notification => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onRemove={removeNotification}
              onMarkAsRead={markAsRead}
            />
          ))
        )}
      </div>
    </div>
  );
};

const NotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { unreadCount, requestNotificationPermission } = useNotifications();

  useEffect(() => {
    requestNotificationPermission();
  }, []);

  return (
    <div className="notification-bell-container">
      <button 
        className="notification-bell"
        onClick={() => setIsOpen(!isOpen)}
      >
        🔔
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount > 99 ? '99+' : unreadCount}</span>
        )}
      </button>
      
      <NotificationDropdown isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
};

export { NotificationBell };
export default NotificationSystem;
